import axios from 'axios';

// Create Axios instance
const api = axios.create({
    baseURL: `${import.meta.env.VITE_API_URL}`,  // Your API URL
});

api.interceptors.request.use((config) => {
    const token = localStorage.getItem('td');
    if (token) {
        config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
}, (error) => {
    return Promise.reject(error);
});

api.interceptors.response.use(
    (response) => response,
    (error) => {
        // If response status is 401 (Unauthorized), it means the token is expired or invalid
        if (error.response && error.response.status === 401) {
            localStorage.removeItem('td');  // Remove token from localStorage
            localStorage.removeItem('tx');  // Remove token from localStorage
            window.location.href = '/';  // Redirect user to login page
        }
        return Promise.reject(error);  // Return error to be handled by the component
    }
);

export default api;

