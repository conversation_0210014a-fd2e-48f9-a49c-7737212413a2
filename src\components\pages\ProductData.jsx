import React, { useEffect, useState } from "react";
import ProductModal from "./ProductModal";

export default function ProductData({ productData }) {
  const [products, setProducts] = useState([]);
  const [filteredProducts, setFilteredProducts] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState("All Categories");
  const [selectedBrand, setSelectedBrand] = useState("All Brands");
  const [categories, setCategories] = useState([]);
  const [brands, setBrands] = useState([]);

  useEffect(() => {
    const productArray = Object.values(productData);
    setProducts(productArray);
    setFilteredProducts(productArray);

    const uniqueCategories = [
      "All Categories",
      ...new Set(productArray.map((product) => product.category)),
    ];
    const uniqueBrands = [
      "All Brands",
      ...new Set(productArray.map((product) => product.brand_name)),
    ];

    setCategories(uniqueCategories);
    setBrands(uniqueBrands);
  }, [productData]);

  const filterProducts = () => {
    let filtered = products;
    if (selectedCategory !== "All Categories") {
      filtered = filtered.filter(
        (product) => product.category === selectedCategory
      );
    }
    if (selectedBrand !== "All Brands") {
      filtered = filtered.filter(
        (product) => product.brand_name === selectedBrand
      );
    }
    setFilteredProducts(filtered);
  };

  const handleCategoryChange = (e) => {
    setSelectedCategory(e.target.value);
  };

  const handleBrandChange = (e) => {
    setSelectedBrand(e.target.value);
  };

  useEffect(() => {
    filterProducts();
  }, [selectedCategory, selectedBrand]);



//   pop up details modal of that products

const [isModalOpen, setIsModalOpen] = useState(false);
const [selectedProduct, setSelectedProduct] = useState(null);

const openModal = (product) => {
  setSelectedProduct(product);
  setIsModalOpen(true);
};

const closeModal = () => {
  setIsModalOpen(false);
  setSelectedProduct(null);  
};
  return (
    <>
      <div className="container">
        <div id="filter" className="row mr-auto ml-auto px-d-3 pt-3 d-flex justify-content-between">
          <div id="category_select" className="select-menu">
            <select
              id="category_select"
              className="form-control select-btn"
              value={selectedCategory}
              onChange={handleCategoryChange}
            >
              {categories.map((category, index) => (
                <option key={index} value={category}>
                  {category}
                </option>
              ))}
            </select>
          </div>

          {/* Brand Select */}
          <div id="brand_select" className="select-menu">
            <div className="select-container">
              <select
                id="brand_select"
                className="form-control select-btn "
                value={selectedBrand}
                onChange={handleBrandChange}
              >
                {brands.map((brand, index) => (
                  <option key={index} value={brand}>
                    {brand}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>
        {/* products List */}

        <div className="row mt-4">
          {filteredProducts.length > 0 ? (
            filteredProducts.map((product,index) => (
              <div
                key={index}
                className="col-12 col-sm-6 col-md-4 col-lg-3 mb-4"
              >
                <div className="card" onClick={() => openModal(product)}>
                  <img src={product.brand_image_url} alt={product.brand_name} />
                  <div className="price">
                    {product.denominations.length > 0 ? (
                      product.denominations.map((value, index) => (
                        <span key={value.product_code}>
                          ₹{value.face_value}
                          {index < product.denominations.length - 1
                            ? " - "
                            : ""}
                        </span>
                      ))
                    ) : (
                      <span>No denominations available</span>
                    )}
                  </div>
                </div>
                <h6 className="text-center" style={{width:"200px"}}>{product.brand_name}</h6>
              </div>
            ))
          ) : (
            <p>No products found for the selected filters.</p>
          )}
        </div>
      </div>

      {isModalOpen && <ProductModal selectedProduct={selectedProduct}
    closeModal={closeModal}  />}
    </>
  );
}
