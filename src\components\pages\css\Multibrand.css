
  /*!  Toast */ 
  .native-toast{position:fixed;background-color:rgba(50,50,50,.8);border-radius:33px;color:#fff;left:50%;text-align:center;padding:10px 20px;opacity:0;z-index:99999;width:18rem;transition:transform .25s,opacity .25s,top .25s;box-sizing:border-box;box-shadow:0 5px 5px rgba(0,0,0,.12),0 6px 6px rgba(0,0,0,.13);-webkit-box-shadow:0 5px 5px rgba(0,0,0,.12),0 6px 6px rgba(0,0,0,.13);-moz-box-shadow:0 5px 5px rgba(0,0,0,.12),0 6px 6px rgba(0,0,0,.13)}.native-toast-bottom{bottom:50px;-ms-transform:translateX(-50%) translateY(50px);transform:translateX(-50%) translateY(50px)}.native-toast-bottom.native-toast-shown{opacity:1;-ms-transform:translateX(-50%) translateY(0);transform:translateX(-50%) translateY(0)}.native-toast-bottom.native-toast-edge{bottom:0}.native-toast-top{top:80px;-ms-transform:translateX(-50%) translateY(-50px);transform:translateX(-50%) translateY(-50px)}.native-toast-top.native-toast-shown{opacity:1;-ms-transform:translateX(-50%) translateY(0);transform:translateX(-50%) translateY(0)}.native-toast-top.native-toast-edge{top:0}.native-toast-center{top:0;-ms-transform:translateX(-50%) translateY(-50px);transform:translateX(-50%) translateY(-50px)}.native-toast-center.native-toast-shown{opacity:1;top:50%;-ms-transform:translateX(-50%) translateY(-50%);transform:translateX(-50%) translateY(-50%)}.native-toast-edge{border-radius:0;width:100%;text-align:left}@media screen and (min-width:40rem){.native-toast:not(.native-toast-edge){max-width:18rem}}.native-toast-error{background-color:#d92727;color:#fff}.native-toast-success{background-color:#62a465;color:#fff}.native-toast-warning{background-color:#fdaf17;color:#fff}.native-toast-info{background-color:#5060ba;color:#fff}[class^=native-toast-icon-]{vertical-align:middle;margin-right:8px}[class^=native-toast-icon-] svg{width:16px;height:16px}
 
 
  /*!
  * Custom CSS 
  */
 body {
     margin: 0;
     font-family: -apple-system,BlinkMacSystemFont,Roboto, "Segoe UI","Helvetica Neue",Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol";
    
     letter-spacing: 0.15px;
     font-weight: 400;
     line-height: 1.5;
     color: #212529;
     text-align: left;
     background-color: #FFFFFF!important;
     font-size: 0.85rem !important;
     
     
 }
 
 #catalogue{
 
     min-height: 60vh;
 }
 
 a{
     color: #555;
 }
 a:hover{
     text-decoration: none;
     color: #555;
 }
 
 :focus {   
     outline-width: 0px;
 }
 
 input:focus, input.form-control:focus {
 
     outline:none !important;
     outline-width: 0 !important;
     box-shadow: none;
     -moz-box-shadow: none;
     -webkit-box-shadow: none;
 }
 
 [type=reset], [type=submit], button, html [type=button] {
     -webkit-appearance: none;
 }
 
 
 .btn:hover{
 background-color: #a80100 !important;
 }
 
 
 
 #loader{
     background-color: #FFF;
     position: fixed;
     top: 0;
     bottom: 0;
     left: 0;
     right:0;
     z-index: 2999;
 }
 
 #loader.visible {
   visibility: visible;
   opacity: 1;
   transition: opacity 0.1s linear;
 }
 
 #loader.hidden {
   visibility: hidden;
   opacity: 0;
   transition: visibility 0s 0.1s, opacity 0.1s linear;
 }
 
 #loader .logo {
     position: relative;
     top: 50%;
     transform: translateY(-50%);
 }
 
 #loaderajax{
     background-color: #FFF;
     position: relative;
     top: 58px;
     margin: 3px;
     bottom: 0;
     left: 0;
     right:0;
     z-index: 2998;
 }
 
 #loaderajax.visible {
   visibility: visible;
   opacity: 1;
   transition: opacity 0.1s linear;
 }
 
 #loaderajax.hidden {
   visibility: hidden;
   opacity: 0;
   transition: visibility 0s 0.1s, opacity 0.1s linear;
 }
 
 #loaderajax .logo {
     position: relative;
     top: 200px;
     transform: translateY(-50%);
 }
     
 .medium{
     font-size: 0.8rem !important;
 }
 
 .qty-box{
     max-width: 7.1em; margin-top: -0.5rem;
     background-color: #FFF;
 }
 
 .remove-item{z-index:9;}
 .balanceZshow{display: none;}
 .emptyShow{margin:0 auto 20px;max-width:500px;}
 
 .loginlogo{		
     max-width: 170px;
     margin-bottom: 10px !important;
     margin-top: 20px !important;
 }
 
 #campaign-list.nav-tabs .nav-item.show .nav-link, #campaign-list.nav-tabs .nav-link.active	 {
     font-weight: 500;	
     
 }
 
 
 
 #campaign-list::-webkit-scrollbar { 
   height: 0 !important;
   display: none; 
 }
 
 #campaign-list.nav-tabs .nav-link {
     border: 0;
     border-top-left-radius: 0rem;
     border-top-right-radius: 0rem;
     color: #555;
     padding: .8rem 1rem;
     white-space: nowrap;
 }
 .header-mobile .dropdown .dropdown-menu {
     box-shadow: 0 13px 27px -5px rgba(50,50,93,0.25), 0 8px 16px -8px rgba(0,0,0,0.3), 0 -6px 16px -6px rgba(0,0,0,0.025);
     border-radius: 0.25rem;	
     background-color: #FFF;
     border: 0px;
     position: absolute !important;
     left: -70px !important;
     top: 28px !important;
 }
 
 @media (min-width: 576px){
     
     .loginPageBg{
         /* background-image: url("../images/others/giftbg.png"); */
         background-repeat: repeat;
         background-color: #004C8F !important;
     }
 }
 
 @media (max-width: 769px){
     #topbanner_desktop{
         display: none;
     }
     #order_history_link{
         display: none;
     }
 }
 @media (min-width: 769px){
     
     #topbanner_mobile{
         display: none;
     }
     .container-main {
         max-width: 100% !important;
     }
     #order_history_mobile{
         display: none;
     }
     
     .loginPage{
         max-width: 1000px; 
         border-radius: 10px; 
         min-height: 60vh; 
         position: fixed; 
         top: 50%; 
         left: 50%; 
         transform: translate(-50%, -50%); 
         -webkit-transform: translate(-50%, -50%); 
         -moz-transform: translate(-50%, -50%); 
         -o-transform: translate(-50%, -50%); 
         -ms-transform: translate(-50%, -50%);
     }
     
 
     
     .loginlogo{
         margin-top: 40px !important;
         
         max-width: 170px;
     }
 
     .loginPageBg{
         /* background-image: url("../images/others/giftbg.png"); */
         background-repeat: repeat;
         background-color: #004C8F !important;
     }
 
     .gradient1{
         background-image: linear-gradient(to bottom, rgba(0,0,0,0), rgba(0,0,0,0.1));  
     }
     .border-desktop {
         border: 1px solid #dee2e6!important;
     }
     .remove-item{
         width: 20px;
         height: 20px;
         position:absolute; 
         
         right:0;
         margin-top: 38px;		
     }	
     
     .remove-item>svg:hover{
         color: #dc3545;
     }
     
     
     .total-amount{
         padding-right: 3em !important;
     }
     .d-px-4{
         padding-left: 1rem !important;
         padding-right: 1rem !important;
     }
     
     
     .d-pt-3{
         padding-top: 1rem !important;		
     }
     .d-mr-3{
         margin-right: 1rem !important;		
     }
     .d-ml-3{
         margin-left: 1rem !important;		
     }
     
     
     
     #pointdetails{
         padding-left: 3rem;
     }
     
     #cartsummary{
         padding-right: 3rem;
         border-right: 1px solid #EEE;
     }
     
     #cartsummary_shipping{
         
         border: 1px solid #EEE;
         padding: 1rem 2rem;
         background-color: #F9f9f9;
     }
     
     #shipping{
         padding-right: 4rem;
         
     }
     
     #shippingaddress{
         padding-left: 5%;
     }
     
     #ordersummary{
         padding-right: 5% !important;
         border-right: 1px solid #EEE;
     }
     
 }
 
 @media screen and (min-width: 1200px) {
     
     .container {
         max-width: 1000px !important;
     }
     .header-mobile .dropdown .dropdown-menu {
         
         left: -130px !important;
         
     }
 }
 @media screen and (min-width: 992px) {
     .product-box{
         cursor: pointer;
         overflow: visible;
         transition: all 0.2s ease;	
         margin: 0 -1px -1px 0;
         
         
     }
     
     .product-box:hover{
         box-shadow: 0 5px 10px rgba(0,0,0,0.12), 0 6px 6px rgba(0,0,0,0.13);
         -webkit-box-shadow: 0 5px 10px rgba(0,0,0,0.12), 0 6px 6px rgba(0,0,0,0.13);
            -moz-box-shadow: 0 5px 10px rgba(0,0,0,0.12), 0 6px 6px rgba(0,0,0,0.13);
         margin-top: -3px;
 
     }
     
     
 
     .product-box:hover .overlay {
       height: 25%;  
     }
 
     .px-d-3{
         padding-left: 1rem;
         padding-right: 1rem;
     }
     
     .remove-item{
         
         
         right:0;
         margin-top: 0px;			
     }
     .w-20 {
       -webkit-box-flex: 0;
           -ms-flex: 0 0 20% !important;
               flex: 0 0 20% !important;
       max-width: 20%;
     }
 
     
 }
 
 /* .overlay {
       position: absolute;
       bottom: 0;
       left: 0;
       right: 0;
       background-color: rgba(0,0,0,0.5);
       overflow: hidden;
       width: 100%;
       height: 0;
       transition: .5s ease;
       border-top-right-radius: 0 !important;
       border-top-left-radius: 0 !important;
     } */
 
 
 .product-box{
     cursor: pointer;
     overflow: visible;
     transition: all 0.2s ease;	
     margin: 0 -1px -1px 0;
     background-color: #FFFFFF;
     position: relative;
     border: 1px solid #eeeeee!important;
     border-radius: 0.7rem !important;
 }
 
 .product-box img{
     /* padding: 8px; */
     border-radius: 0.7rem !important;
     
 }
 
 .product-box .card-body{
     margin: 0px 10px 15px 10px;
     padding: 0;
 }
 
 .product-box .card-title{
     color: #777 !important;
     height: 35px;
 }
 
 .product-box .card-body>h6 {
   
   overflow: hidden;
   o-text-overflow: ellipsis;
   text-overflow: ellipsis;
 }
 
 .product-box .card-body>h6:hover { 
   overflow: visible;  
   display: inline-block;
     
 }
 
 .logo{
     max-width: 180px;
 }
 
 
     
 .navbar-icon-badge {
     position: absolute;
     margin-top: -10px;
     margin-left: -10px;
     width: 20px;
     height: 20px;
     text-align: center;
     color: #FFFFFF;
     border-radius: 50%;    
     opacity: 0.5;
     font-size: 0.7rem;
     line-height: 20px;
 }
 
 .c-p{
     cursor: pointer;
 }
 .border-rounded{
     border-radius: 2rem !important;
 }
 
 /* .card{
     background-color: #f7f7f7!important;
 } */
 
 
 
 
 
 .shadow {
     box-shadow: 0 .5rem 1rem rgba(0,0,0,.15)!important;
 }
 
 .shadow-sm {
     box-shadow: 0 .125rem .25rem rgba(0,0,0,.075)!important;
 }
 
 .row-alt{
     display: -webkit-box; 
     display: -ms-flexbox; 
     display: flex; -ms-flex-wrap: wrap; 
     flex-wrap: wrap;
 }
 
 .footer {
   position: fixed;
   bottom: 0;
   width: 100%;
   /* Set the fixed height of the footer here */
   height: 60px;
   line-height: 60px; /* Vertically center the text there */
   background-color: #cccccc;
 }
 
 .footer > .container {
   padding-right: 15px;
   padding-left: 15px;
 }
 
 
 
 
 .videowrapper {
     float: none;
     clear: both;
     width: 100%;
     position: relative;
     padding-bottom: 56.25%;
     padding-top: 0px;
     
     height: 0;
     display: inline-block;
     position: relative;
 }
 .videowrapper iframe {
     position: absolute;
     top: 0;
     left: 0;
     width: 100%;
     height: 100%;
     padding: 0 13px;
 }
 
 .videowrapper.ended::after {
     content:"";
     position: absolute;
     top: 0;
     left: 0;
     bottom: 0;
     right: 0;
     margin: 0 13px;
     cursor: pointer;
     background-color: rgba(0,0,0,0.8);
     background-repeat: no-repeat;
     background-position: center; 
     background-size: 64px 64px;
     background-image: url(data:image/svg+xml;utf8;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMjgiIGhlaWdodD0iMTI4IiB2aWV3Qm94PSIwIDAgNTEwIDUxMCI+PHBhdGggZD0iTTI1NSAxMDJWMEwxMjcuNSAxMjcuNSAyNTUgMjU1VjE1M2M4NC4xNSAwIDE1MyA2OC44NSAxNTMgMTUzcy02OC44NSAxNTMtMTUzIDE1My0xNTMtNjguODUtMTUzLTE1M0g1MWMwIDExMi4yIDkxLjggMjA0IDIwNCAyMDRzMjA0LTkxLjggMjA0LTIwNC05MS44LTIwNC0yMDQtMjA0eiIgZmlsbD0iI0ZGRiIvPjwvc3ZnPg==);
 }
 .videowrapper.paused::after {
     content:"";
     position: absolute;
     top: 0px;
     left: 0;
     bottom: 0px;
     right: 0;
     margin: 0 13px;
     cursor: pointer;
     background-color: rgba(0,0,0,0.8);
     background-repeat: no-repeat;
     background-position: center; 
     background-size: 60px 60px;
     background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iaXNvLTg4NTktMSI/Pg0KPCEtLSBHZW5lcmF0b3I6IEFkb2JlIElsbHVzdHJhdG9yIDE5LjAuMCwgU1ZHIEV4cG9ydCBQbHVnLUluIC4gU1ZHIFZlcnNpb246IDYuMDAgQnVpbGQgMCkgIC0tPg0KPHN2ZyB2ZXJzaW9uPSIxLjEiIGlkPSJDYXBhXzEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHg9IjBweCIgeT0iMHB4Ig0KCSB2aWV3Qm94PSIwIDAgNTggNTgiIHN0eWxlPSJlbmFibGUtYmFja2dyb3VuZDpuZXcgMCAwIDU4IDU4OyIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSI+DQo8Y2lyY2xlIHN0eWxlPSJmaWxsOiNFQkJBMTY7IiBjeD0iMjkiIGN5PSIyOSIgcj0iMjkiLz4NCjxnPg0KCTxwb2x5Z29uIHN0eWxlPSJmaWxsOiNGRkZGRkY7IiBwb2ludHM9IjQ0LDI5IDIyLDQ0IDIyLDI5LjI3MyAyMiwxNCAJIi8+DQoJPHBhdGggc3R5bGU9ImZpbGw6I0ZGRkZGRjsiIGQ9Ik0yMiw0NWMtMC4xNiwwLTAuMzIxLTAuMDM4LTAuNDY3LTAuMTE2QzIxLjIwNSw0NC43MTEsMjEsNDQuMzcxLDIxLDQ0VjE0DQoJCWMwLTAuMzcxLDAuMjA1LTAuNzExLDAuNTMzLTAuODg0YzAuMzI4LTAuMTc0LDAuNzI0LTAuMTUsMS4wMzEsMC4wNThsMjIsMTVDNDQuODM2LDI4LjM2LDQ1LDI4LjY2OSw0NSwyOXMtMC4xNjQsMC42NC0wLjQzNywwLjgyNg0KCQlsLTIyLDE1QzIyLjM5NCw0NC45NDEsMjIuMTk3LDQ1LDIyLDQ1eiBNMjMsMTUuODkzdjI2LjIxNUw0Mi4yMjUsMjlMMjMsMTUuODkzeiIvPg0KPC9nPg0KPGc+DQo8L2c+DQo8Zz4NCjwvZz4NCjxnPg0KPC9nPg0KPGc+DQo8L2c+DQo8Zz4NCjwvZz4NCjxnPg0KPC9nPg0KPGc+DQo8L2c+DQo8Zz4NCjwvZz4NCjxnPg0KPC9nPg0KPGc+DQo8L2c+DQo8Zz4NCjwvZz4NCjxnPg0KPC9nPg0KPGc+DQo8L2c+DQo8Zz4NCjwvZz4NCjxnPg0KPC9nPg0KPC9zdmc+DQo=);
 }
 
 @media screen and (max-width: 768px) {
     .fullscreenModal .modal-header .close {
         color: #FFF;
     }
     
     .remove-item{
         width: 20px;
         height: 20px;
         position:absolute; 
         
         right:0;
         margin-top: 38px;			
     }
     
     .remove-item>svg:hover{
         color: #dc3545;
     }
 }
 
 
 
 
 
 .checkmark {
     width: 70px;
     height: 70px;
     border-radius: 50%;
     display: block;
     stroke-width: 2;
     stroke: #4bb71b;
     stroke-miterlimit: 10;
     box-shadow: inset 0px 0px 0px #4bb71b;
     animation: fill .4s ease-in-out .4s forwards, scale .3s ease-in-out .9s both;
     position:relative;
     top: 5px;
     right: 5px;
    margin: 0 auto;
 }
 .checkmark__circle {
     stroke-dasharray: 166;
     stroke-dashoffset: 166;
     stroke-width: 2;
     stroke-miterlimit: 10;
     stroke: #4bb71b;
     /* fill: #fff; */
     animation: stroke 0.6s cubic-bezier(0.65, 0, 0.45, 1) forwards;
  
 }
 
 .checkmark__check {
     transform-origin: 50% 50%;
     stroke-dasharray: 48;
     stroke-dashoffset: 48;
     animation: stroke 0.3s cubic-bezier(0.65, 0, 0.45, 1) 0.8s forwards;
 }
 
 @keyframes stroke {
     100% {
         stroke-dashoffset: 0;
     }
 }
 
 @keyframes scale {
     0%, 100% {
         transform: none;
     }
 
     50% {
         transform: scale3d(1.1, 1.1, 1);
     }
 }
 
 @keyframes fill {
     100% {
         box-shadow: inset 0px 0px 0px 30px #4bb71b;
     }
 }
 
 
 
 /* Cart Icon Ripple effect */
 
 .cartQty::before {
   content: "";
   position: absolute;
   right: 0px;
   width: 100%;
   height: 100%;
   background-color: inherit;
   border-radius: 50%;
   z-index: -1;
   animation: ripple 1.5s ease-out;
 }
 
 
 .cartQty:first-child {
   background-color: #ff414d;
 }
 
 .cartQty:first-child::before {
   animation-delay: 0.2s;
 }
 
 
 @keyframes ripple {
   from {
     opacity: 1;
     transform: scale(0);
   }
   to {
     opacity: 0;
     transform: scale(3);
   }
 }
 
 /* Cart Icon Ripple effect */
 
 
 /* Animation */
 
 @keyframes fadeInUp {
     from {
         transform: translate3d(0,40px,0)
     }
 
     to {
         transform: translate3d(0,0,0);
         opacity: 1
     }
 }
 
 @-webkit-keyframes fadeInUp {
     from {
         transform: translate3d(0,40px,0)
     }
 
     to {
         transform: translate3d(0,0,0);
         opacity: 1
     }
 }
 
 .animated {
     animation-duration: 1s;
     animation-fill-mode: both;
     -webkit-animation-duration: 1s;
     -webkit-animation-fill-mode: both
 }
 
 .animatedFadeInUp {
     opacity: 0
 }
 
 .fadeInUp {
     opacity: 0;
     animation-name: fadeInUp;
     -webkit-animation-name: fadeInUp;
 }
 
 .toast {
     min-width: 275px;
 }
 
 #Rpt_ProductImage{
     margin-left: -10px !important;
     margin-right: -10px !important;
     
 }
 
 
   .fade-in-up {
     opacity: 0;
     transform: translateY(10%);
     transition: opacity 0.7s ease-in-out, transform 0.9s ease-in-out;
   }
   
   .fade-in-up.show {
     opacity: 1;
     transform: translateY(0);
   }
 
 
 
 
 
   
 
 .offerBand{
     background-color: #004C8F;
     color: #FFFFFF;
     padding: 0px 7px;
     position: absolute;
     bottom: 7px;
     left: -7px;
     z-index:998; 
     border-radius: 0px 1rem 1rem 0;
     padding-top: -0.5rem!important;
 }
 
 
 
   
   .ribbon-edge-topleft,
   .ribbon-edge-topright,
   .ribbon-edge-bottomleft,
   .ribbon-edge-bottomright {
     position: absolute;
     z-index: 1;
     border-style:solid;
     height:0px;
     width:0px;
 }
 
   .ribbon-edge-topleft,
   .ribbon-edge-topright {
 }
 
   .ribbon-edge-bottomleft,
   .ribbon-edge-bottomright {
     top: 10px;
 }
 
   .ribbon-edge-topleft,
   .ribbon-edge-bottomleft {
     left: -10px;
     border-color: transparent #023059 transparent transparent;
 }
 
   .ribbon-edge-topleft {
     bottom: 33px;
     border-width: 5px 7px 0 3px;
 }
   .ribbon-edge-bottomleft {
     border-width: 0 10px 0px 0;
 }
 
   .ribbon-edge-topright,
   .ribbon-edge-bottomright {
     left: 200px;
     border-color: transparent transparent transparent #9B1724;
 }
 
   .ribbon-edge-topright {
     top: 0px;
     border-width: 0px 0 0 10px;
 }
   .ribbon-edge-bottomright {
     border-width: 0 0 5px 10px;
 }
 
 
 @-webkit-keyframes flow {
     0% { left:-20px;opacity: 0;}
     50% {left:100px;opacity: 0.3;}
     100%{ left:180px;opacity: 0;}
 }
 @keyframes flow {
     0% { left:-20px;opacity: 0;}
     50% {left:100px;opacity: 0.3;}
     100%{ left:180px;opacity: 0;}
 }
 
 .glow{ 
 background: rgb(255,255,255); 
 width:40px; 
 height:100%; 
 z-index:999; 
 position:absolute;
 bottom: 0px;
 -webkit-animation: flow 1.5s linear infinite;
 -moz-animation: flow 1.5s linear infinite;
 -webkit-transform: skew(20deg);
        -moz-transform: skew(20deg);
          -o-transform: skew(20deg);
 background: -moz-linear-gradient(left, rgba(255,255,255,0) 0%, rgba(255,255,255,0) 1%, rgba(255,255,255,1) 100%); /* FF3.6+ */
 background: -webkit-gradient(linear, left top, right top, color-stop(0%,rgba(255,255,255,0)), color-stop(1%,rgba(255,255,255,0)), color-stop(100%,rgba(255,255,255,1))); /* Chrome,Safari4+ */
 background: -webkit-linear-gradient(left, rgba(255,255,255,0) 0%,rgba(255,255,255,0) 1%,rgba(255,255,255,1) 100%); /* Chrome10+,Safari5.1+ */
 background: -o-linear-gradient(left, rgba(255,255,255,0) 0%,rgba(255,255,255,0) 1%,rgba(255,255,255,1) 100%); /* Opera 11.10+ */
 background: -ms-linear-gradient(left, rgba(255,255,255,0) 0%,rgba(255,255,255,0) 1%,rgba(255,255,255,1) 100%); /* IE10+ */
 background: linear-gradient(to right, rgba(255,255,255,0) 0%,rgba(255,255,255,0) 1%,rgba(255,255,255,1) 100%); /* W3C */
 filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#00ffffff', endColorstr='#ffffff',GradientType=1 ); /* IE6-9 */ border-left:1px solid #fff;}
 
 
 
 
 
 
 .code_label {
     width:50px; 
     display:block; 
     float:left; 
     border-right:1px solid #FFFFFF;
 }
 
 /* .code {
     width:240px; 
     float:left; 
     color:black; 
     letter-spacing: 1px;
     margin-top: 0.15rem!important;
 
 } */
 /* .copy {
     float:left; 
     border-radius: 20px;	
     background-color: white;
     border-color: #ECECEC;
     width: 25px;
 } */
 
 .copy_pin {
     margin-left: 10px; 
     border-radius: 20px; 
     border: 1px solid #eee; 
     background-color: #ffffff; 
     padding: 4px; 
     width: 30px; 
     display: inline-block;
 }
         
 .coupon_p1_modal {
     background-color: #FFFFFF;	
     border: 1px solid #dddddd;
     filter: drop-shadow(0 3px 5px rgba(0, 0, 0, 0.1));
     border-bottom: 0;
     border-top-left-radius: 10px;
     border-top-right-radius: 10px; 
     min-height: 315px;
     overflow: hidden;
     padding-top:10px;
     
 }
 
 .coupon_p2_modal {
     background-color: #e0e0e0;	
     border-radius: 7px;
     position: sticky;
     z-index: 3;
     height: 40px;
     margin-left: -20px;
     margin-right: -20px;
     padding: 7px;
     line-height: 25px;
 }
 
 .coupon_p3_modal {	
     border-bottom-left-radius: 10px;	
     height: 105px;
     width: 4%;
     float:left;	
     background-image: radial-gradient( circle at 0 40%, transparent 10px, #fff 11px );
 }
 
 .coupon_p4_modal {
     background-color: #FFFFFF;
     position: sticky;
     z-index: 3;
     height: 105px;
     width: 92%;
     float:left;	
     overflow: hidden;
     text-align: center; 
     padding-top:6px; 
 }
 
 .coupon_p5_modal {		
     border-bottom-right-radius: 10px; 	
     height: 105px;
     width: 4%;
     float:left;
     background-image: radial-gradient( circle at 100% 40%, transparent 10px, #fff 11px );	
 }
 
 
 /* Modal */
 
 .modal-voucher {
   position: fixed;
   width: 100vw;
   height: 100vh;
   opacity: 0;
   /* visibility: hidden; */
   transition: all 0.3s ease;
   top: 0;
   left: 0;
   display: flex;
   align-items: center;
   justify-content: center;
   z-index: 1051;
   
 }
 .modal-voucher.open {
   visibility: visible;
   opacity: 1;
   transition-delay: 0s;
 }
 .modal-bg {
   position: absolute;
   background: rgba(0, 0, 0, 0.7);
   width: 100%;
   height: 100%;
   z-index: 1052;
 }
 .modal-container {
   border-radius: 10px;
   background: #ffffff00;
   position: relative;
   padding: 30px;
   text-align: center;
   z-index: 1053;
 }
 
 .close_modal{
     text-align: right;
     margin-right: 10px;
     cursor: pointer;
 }
 
 /* Modal */
 
 
 
 
 .select2-container .select2-selection--single {
     height: 36px !important;
     box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
     border: 1px solid #ddd;
     border-radius: 6px;
     background-color: #efefef;
 }
 
 .select2-container--default .select2-selection--single .select2-selection__rendered {    
     line-height: 35px;
     font-size: 13px;
     padding-left: 15px;
     padding-right: 30px;
 }
 .select2-container--default .select2-selection--single .select2-selection__arrow {
     height: 35px; 
     width: 35px;   
 }
 
 .select2-container--default .select2-selection--single .select2-selection__arrow b {   
     border-width: 6px 6px 0px 6px;
     top: 50%;
     border-color: #555 transparent transparent transparent;
 }
 
 .select2-container--default.select2-container--open .select2-selection--single .select2-selection__arrow b {   
     border-color: transparent transparent #555 transparent;
     border-width: 0 6px 6px 6px;
 }
 
 .select2-container--default .select2-selection--single .select2-selection__placeholder {
     color: #555;
 }
 
 .search-menu{
     width: 48%;
     max-width: 220px;
     float: right;
 }
 
 
 /* Category dropdown */
 
   
   .select-menu {	
     /* margin: 0px auto 0px 0px; */
     width: 40%;
     max-width: 220px;
   }
   .select-menu .select-btn {
     display: flex;
     height: 35px;
     background: #efefef;
     padding: 6px 10px 8px 15px;
     font-size: 15px;
     font-weight: 400;
     border-radius: 6px;
     align-items: center;
     cursor: pointer;
     justify-content: space-between;
     border: 1px solid #ddd;
     box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
   }
   .select-menu .options {
     position: absolute;
     width: 250px;
     overflow-y: auto;
     max-height: 295px;
     padding: 10px;
     margin-top: 10px;
     border-radius: 8px;
     background: #fff;
     box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
     animation-name: fadeInDown;
     -webkit-animation-name: fadeInDown;
     animation-duration: 0.35s;
     animation-fill-mode: both;
     -webkit-animation-duration: 0.35s;
     -webkit-animation-fill-mode: both;
   }
   .select-menu .options .option {
     display: flex;
     height: 35px;
     cursor: pointer;
     padding: 0 16px;
     border-radius: 8px;
     align-items: center;
     background: #fff;
   }
   .select-menu .options .option:hover {
     background: #f2f2f2;
   }
   .select-menu .options .option i {
     font-size: 25px;
     margin-right: 12px;
   }
   .select-menu .options .option .option-text {
     font-size: 15px;
     color: #333;
   }
   
   .select-btn i {
     font-size: 25px;
     transition: 0.3s;
   }
   
   .select-menu.active .select-btn i {
     transform: rotate(-180deg);
   }
   .select-menu.active .options {
     display: block;
     opacity: 0;
     z-index: 10;
     animation-name: fadeInUp;
     -webkit-animation-name: fadeInUp;
     animation-duration: 0.4s;
     animation-fill-mode: both;
     -webkit-animation-duration: 0.4s;
     -webkit-animation-fill-mode: both;
   }
   
   @keyframes fadeInUp {
     from {
       transform: translate3d(0, 30px, 0);
     }
     to {
       transform: translate3d(0, 0, 0);
       opacity: 1;
     }
   }
   @keyframes fadeInDown {
     from {
       transform: translate3d(0, 0, 0);
       opacity: 1;
     }
     to {
       transform: translate3d(0, 20px, 0);
       opacity: 0;
     }
   }
   
   
   @font-face {
     font-family: boxicons;
     font-weight: 400;
     font-style: normal;	
     src: url(https://unpkg.com/boxicons@2.1.1/fonts/boxicons.eot);
     src: url(https://unpkg.com/boxicons@2.1.1/fonts/boxicons.eot) format('embedded-opentype'),url(https://unpkg.com/boxicons@2.1.1/fonts/boxicons.woff2) format('woff2'),url(https://unpkg.com/boxicons@2.1.1/fonts/boxicons.woff) format('woff'),url(https://unpkg.com/boxicons@2.1.1/fonts/boxicons.ttf) format('truetype'),url(https://unpkg.com/boxicons@2.1.1/fonts/boxicons.svg?#boxicons) format('svg')
 
 }
 
 
   .bx {
     font-family: boxicons !important;
     font-weight: 400;
     font-style: normal;
     font-variant: normal;
     line-height: 1;
     text-rendering: auto;
     display: inline-block;
     text-transform: none;    
     -webkit-font-smoothing: antialiased;
     -moz-osx-font-smoothing: grayscale;
 }
 .bx-chevron-down:before{content:"\ea4a"}
 .bx-chevron-down-circle:before{content:"\ea4b"}
 
 /* Category dropdown */
 
 
 /* dot dropdown css  */
 /* Basic Dropdown Styles */
 .dropdown-menu {
     
     position: absolute;
     top: 74px;
     right: 340px;
     background-color: white;
     border: 1px solid #ccc;
     box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
     z-index: 1;
     padding: 10px;
     border-radius: 5px;
     min-width: 180px;
     transition: opacity 0.3s ease, transform 0.3s ease;
     transform: translateY(-10px); /* Slide up effect */
   }
   
   /* Dropdown active state */
   .dropdown-menu.show {
     display: block; /* Show the dropdown */
     opacity: 1; /* Fade in */
     transform: translateY(0); /* Slide down */
   }
   
   .dropdown-item {
     padding: 8px 12px;
     cursor: pointer;
     text-decoration: none;
     color: #333;
   }
   
   .dropdown-item:hover {
     background-color: #f1f1f1;
   }
   
   .dropdown-item small {
     color: #999;
   }
 
 /* cards */
 
 .card {
     position: relative;
     width: 200px;
     height: 80px;
     text-align: center;
     cursor: pointer;
     border: 1px solid #ccc;
     border-radius: 8px;
     overflow: hidden;
   }
   
   .card img {
     width: 100%;
     height: auto;
   }
   
   .card .price {
     position: absolute;
     bottom: 0;
     background-color: rgba(0, 0, 0, 0.6);
     color: white;
     width: 100%;
     padding: 5px;
     font-size: 14px;
     display: block;
     opacity: 0;
     transition: opacity 0.3s ease;
   }
   
   .card:hover .price {
     opacity: 1;
   }
   
   
   .modal {
     background: rgba(255, 255, 255, 0.39);
     padding: 20px;
     border-radius: 8px;
     text-align: center;
     box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
   }
   
   .modal>button {
     margin-top: 10px;
     padding: 10px 20px;
     border: none;
     background-color: #007bff;
     color: white;
     border-radius: 5px;
     cursor: pointer;
   }
   
   
   
 
 
   /* vocher card modal */
   /* Modal Overlay */
 .modal-overlay {
     position: fixed;
     top: 0;
     left: 0;
     width: 100%;
     height: 100%;
     background: rgba(0, 0, 0, 0.5);
     display: flex;
     align-items: center;
     justify-content: center;
     z-index: 1000;
   }
   
   /* Modal Content */
   .modal-content {
     background: #fff;
     /* width: 70%; */
     border-radius: 8px;
     box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
     overflow: hidden;
     position: relative;
     font-family: 'Arial', sans-serif;
   }
 
   .modal-content-cart{
     background: #fff;
     width: 100%;
     border-radius: 8px;
     box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
     overflow: hidden;
     position: relative;
     font-family: 'Arial', sans-serif;
   }
   
   .close-modal {
     float: right;
     font-size: 1.5rem;
     font-weight: 700;
     line-height: 1;
     color: #000;
     text-shadow: 0 1px 0 #fff;
     opacity: .5;
     padding: 0;
     background-color: transparent;
     border: 0;
   }
   .close-modal:hover {
     color: #000;
     text-decoration: none;
     background-color: transparent !important;
 }
 .pt-3, .py-3 {
     padding-top: 1rem !important;
 }
   /* Header Section */
   .gift-card-modal h2 {
     font-size: 24px;
     color: #b22222;
     /* text-align: center; */
     margin-top: 20px;
     padding-left: 40px;
   }
   
   .gift-card-modal img {
     display: block;
     margin: 10px auto;
     width: 100px;
     height: auto;
   }
   
   /* Tabs Navigation */
   .tabs {
     display: flex;
     justify-content: space-evenly;
     background-color: #f9f9f9;
     border-bottom: 2px solid #ddd;
     height: 50px;
   }
   
   .tabs button {
     flex: 1;
     padding: 0px;
     text-align: center;
     border: none;
     background: none;
     font-size: 16px;
     cursor: pointer;
     color: #555;
   }
   
   .tabs button.active {
     font-weight: bold;
     color: #b22222;
     border-bottom: 3px solid #b22222;
     padding: 0;
   }
   
   .tab-content {
     color: #333;
     text-align: start;
     font-size: 14px;
   }
   
   /* Amount Selection Section */
   .amount-selection {
     padding: 20px;
     text-align: center;
   }
   
   .amount-selection h4 {
     font-size: 16px;
     margin-bottom: 10px;
   }
   
   .amount-selection button {
     margin: 5px;
     padding: 10px 20px;
     font-size: 14px;
     cursor: pointer;
     border: 1px solid #ddd;
     background-color: #fff;
     border-radius: 5px;
     transition: background-color 0.3s ease;
   }
   
   .amount-selection button.selected,
   .amount-selection button:hover {
     background-color: #b22222;
     color: #fff;
   }
   
   /* Quantity Selection */
   .quantity-selection {
     display: flex;
     flex-direction: column;
     align-items: baseline;
     /* gap: 14px; */
     /* padding: 0 20px 15px 20px; */
     margin-bottom: 4px;
   }
   .quantity-selection>h4{
     margin-bottom: 0;
     font-size: 18px;
   }
   
   .quantity-selection input {
     width: 100%;
     /* text-align: center; */
     padding: 5px;
     font-size: 14px;
     border: 1px solid #ddd;
     border-radius: 4px;
   }
   
   /* Total Amount */
   .total-amount {
     font-size: 16px;
     font-weight: bold;
 
     padding: 10px;
   }
   
   /* Add to Cart Button */
   .add-to-cart {
     display: block;
     width: calc(100% - 40px);
     margin: 20px auto;
     padding: 12px 0;
     font-size: 16px;
     font-weight: bold;
     text-align: center;
     background-color: #b22222;
     color: white;
     border: none;
     border-radius: 5px;
     cursor: pointer;
     transition: background-color 0.3s ease;
   }
   
   .add-to-cart:hover {
     background-color: #900;
   }
   
   /* Redeemable Section */
   .redeemable {
     display: flex;
     gap: 20px;
     font-size: 14px;
     padding: 10px 0;
   }
   
   .redeemable span {
     display: flex;
     align-items: center;
     gap: 5px;
   }
 
   /* Modal Layout */
 .modal-row {
     display: flex;
     gap: 20px;
     padding: 20px;
   }
   
   /* Left Partition */
   .left-partition {
     width: 50%;
     padding-right: 10px;
     border-right: 1px solid #ddd;
   }
   
   .left-partition img {
     display: block;
     margin: 10px auto;
     width: 280px;
     height: auto;
   }
   
   .amount-selection {
     margin-bottom: 20px;
   }
   
   .amount-selection button {
     margin: 5px;
     padding: 10px 20px;
     cursor: pointer;
     border: 1px solid #ddd;
     border-radius: 5px;
   }
   
   .add-to-cart {
     width: 100%;
     margin-top: 20px;
     padding: 12px;
     background-color: #b22222;
     color: white;
     border: none;
     border-radius: 5px;
     font-weight: bold;
     text-align: center;
   }
   
   
   /* Right Partition */
   .right-partition {
     width: 50%;
   }
   
   .tabs {
     display: flex;
     gap: 10px;
     margin-bottom: 10px;
   }
 
   
   .redeemable {
     font-size: 14px;
     padding-top: 10px;
   }
 
 
   /* toast */
   .toast-body {
     display: flex;
     align-items: center;
     padding: 10px 15px;
     border-radius: 5px;
     background-color: #28a745; /* Green background */
     color: #fff;
     font-size: 16px;
     box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
   }
   
   .checkmark {
     display: inline-block;
     width: 30px;
     height: 30px;
     margin-right: 15px;
   }
   
   .checkmark__circle {
     stroke: #fff;
     stroke-width: 2;
   }
   
   .checkmark__check {
     stroke: #fff;
     stroke-width: 2;
     stroke-linecap: round;
     stroke-linejoin: round;
     transition: all 0.3s ease;
   }
   
   .toast-body .text-white {
     font-weight: bold;
   }
   
   .toast-body {
     opacity: 1;
     transition: opacity 0.3s ease-in-out;
   }
   
   .toast-body.hidden {
     opacity: 0;
   }
 
 
   .quantity-controls {
     display: flex;
     align-items: center;
     border:1px solid #5c5c5c00;
     padding: 2px;
   }
   
   .quantity-controls button {
     padding: 5px 10px;
     font-size: 18px;
     margin: 0 5px;
     cursor: pointer;
     border: none;
     /* background-color: none; */
   }
   
   
   
   
 
   
   
 
 
 