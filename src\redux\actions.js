// Actions to update cart and wallet balance

// Update wallet balance
export const updateWalletBalance = (newBalance) => {
    return {
      type: 'UPDATE_WALLET_BALANCE',
      payload: newBalance,
    };
  };
  
  // Add to cart
  export const addToCart = (product) => {
    return {
      type: 'ADD_TO_CART',
      payload: product,
      
    };
  };
  
  // Update quantity of an existing product in cart
  export const updateProductQuantity = (payload) => ({
    type: 'UPDATE_PRODUCT_QUANTITY',
    payload,
  });
  

  // Actions to remove a product from the cart
export const removeProductFromCart = (productCode) => ({
    type: 'REMOVE_PRODUCT_FROM_CART',
    payload: productCode,
  });
  



export const updateMaxQuantity = (maxQuantity) => ({
  type: "UPDATE_MAX_QUANTITY",
  payload: maxQuantity,
});

export const clearCart = () => ({
  type: 'CLEAR_CART',
});

import { toast } from 'react-toastify';

// Show Success Toast
export const showSuccessToast = (message) => {
  toast.success(message);
};

// Show Error Toast
export const showErrorToast = (message) => {
  toast.error(message);
};

// Show Info Toast
export const showInfoToast = (message) => {
  toast.info(message);
};
