import {BrowserRouter,Route,Routes} from 'react-router-dom'
import Login from './components/Login'
import Otp from './components/Otp'
import Monobrand from './components/pages/Monobrand'
import Token from './Token'
import Multibrand from './components/pages/Multibrand'


function App() {

  return (
    <>
      <BrowserRouter>
      <Routes>
        <Route path="" element={<Login/>}/>
        <Route path='/otp' element={<Otp/>}/>

        
        <Route path='/mono_brand_voucher' element={<Monobrand/>}/>
        <Route path='/multi_brand_voucher' element={<Multibrand/>}/>
      </Routes>
      </BrowserRouter>
    </>
  )
}

export default App
