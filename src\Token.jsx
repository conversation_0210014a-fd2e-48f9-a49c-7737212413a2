import React from 'react';
import { Route, Navigate } from 'react-router-dom';

export default function Token({ element, ...rest }) {
    

    const token = localStorage.getItem('td');
    const tokenExpiry = localStorage.getItem('tx');
    
    const currentTime = Math.floor(Date.now() / 1000);
  
    // If there's no token or token has expired, redirect to home
    if (!token || !tokenExpiry || currentTime > tokenExpiry) {
      // Redirect to home page or a specific route with the query parameters preserved
      return <Navigate to={`/${window.location.search}`} replace />;
    }
  
    // If token is valid, render the passed component (rest of the route)
    return element;
}
