import React, { useEffect, useState } from "react";
import axios from "axios";
import { useNavigate } from "react-router-dom";
import CryptoJS from "crypto-js";
const SECRET_KEY = "your_secret_key";
import api from "../Api.jsx";
const encryptData = (data) => {
  return CryptoJS.AES.encrypt(data, SECRET_KEY).toString();
};



export default function Login() {
  const [voucherCode, setVoucherCode] = useState("");
  const [contactInfo, setContactInfo] = useState("");
  const [privacyPolicy, setPrivacyPolicy] = useState(false);
  const [error, setError] = useState("");
  const [data, setData] = useState("");
  const navigate = useNavigate();

  // call program setting  api

  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const v = params.get("v");
    const p = params.get("p");
    localStorage.setItem("p", p);
    {
      v && setVoucherCode(v);
    }

    const fetchData = async () => {
      try {
        const response = await api.get(
          `${
            import.meta.env.VITE_API_URL
          }/api/auth/get_program_settings?pid=${p}`
        );
        {
          response.status === 200 && setData(response.data.data.settings);
        }
        // console.log(response);
        //   const data = await response.json();
        //   setApiData(data);
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };

    fetchData();
  }, []);

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!voucherCode) {
      setError("Please Enter a valid Voucher Code");
      return;
    }
    const encryptedToken = encryptData(voucherCode);
    localStorage.setItem("token", encryptedToken);

    if (!contactInfo) {
      if (data.voucher_link_authentication_mode === 3) {
        setError("Please Enter Your Pin");
        return;
      } else if (data.voucher_link_authentication_mode === 2) {
        setError("Please Enter  Your  Email");
        return;
      } else {
        setError("Please Enter Your Mobile Number");
        return;
      }
    }

    let contactFieldName = "mobile";
    // data.voucher_link_authentication_mode
    switch (data.voucher_link_authentication_mode) {
      case 2:
        contactFieldName = "email";
        break;
      case 3:
        contactFieldName = "pin";
        break;
      default:
        contactFieldName = "mobile";
    }

    const formData = {
      voucher_code: voucherCode,
      [contactFieldName]: contactInfo,
    };

    try {
      const response = await axios.post(
        `${import.meta.env.VITE_API_URL}/api/auth/login`,
        formData
      );

      if (response.status === 200) {
        const tokenData = response.data?.data?.tokenData;
        console.log(tokenData);

        if (tokenData) {
          localStorage.setItem("tx", tokenData.tokenExpiry);
          localStorage.setItem("td", tokenData.token);

          if (response.data.data.link_token === "1") {
            navigate(`/mono_brand_voucher`);
          } else if (response.data.data.link_token === "2") {
            navigate(`/multi_brand_voucher`);
          }
        } else {
          navigate("/otp", {
            state: {
              userId: response.data.data.user_id,
              data: contactInfo,
              style: data,
              link_token: response.data.data.link_token,
            },
          });
        }
      }
    } catch (error) {
      setError(error.response.data.message);
      console.log("Error fetching data:", error);
    }
  };

  // field acc to auth mode

  const getInputDetails = () => {
    switch (data.voucher_link_authentication_mode) {
      case 1:
        return {
          inputType: "tel",
          label: "Mobile Number",
          placeholder: "Enter Mobile Number",
        };
      case 2:
        return {
          inputType: "email",
          label: "Email Address",
          placeholder: "Enter Email Address",
        };
      case 3:
        return {
          inputType: "text",
          label: "PIN",
          placeholder: "Enter PIN",
        };
      default:
        return {
          inputType: "text",
          label: "Contact Information",
          placeholder: "Enter Information",
        };
    }
  };
  const { inputType, label } = getInputDetails();

  return (
    <>
      <div className="relative flex flex-col md:flex-row min-h-screen w-full">
        {/* Mobile Banner */}
        <div className="block md:hidden w-full">
          {data.login_page_banner && (
            <img
              className="w-full h-[220px] sm:h-[250px] object-cover"
              src={data.login_page_banner}
              alt="background"
            />
          )}
        </div>

        {/* Right Column (Form) */}
        <div className="w-full md:w-[28em] lg:w-[30em] xl:w-[36em] 2xl:w-[40em] bg-white flex flex-col justify-center relative px-4 py-8 md:py-20 md:px-16 lg:px-20 xl:px-24 2xl:px-28 text-center md:absolute md:top-0 md:right-0 md:min-h-screen">
          {/* Powered By Logo */}
          <div className="absolute right-4 top-4 md:right-[-30px] lg:right-[-60px] xl:right-[-90px] md:top-10 lg:top-6 xl:top-5 flex items-center space-x-1 text-xs z-10">
            <span className="mt-[-30px] mr-1 text-xs">Powered By</span>
            <img
              src="./images/rg.png"
              height={25}
              alt="powered-by"
              className="mb-0 h-[25px]"
            />
          </div>

          {/* Form Container */}
          <div className="relative text-center px-6 py-8 md:px-0 md:py-0">
            <div className="text-left mb-0 ml-[-5px]">
              <img width="55" src="./images/2883833.png" alt="icon" />
            </div>

            <form onSubmit={handleSubmit} className="w-full mt-4">
              {/* Voucher Code Field */}
              <div className="relative pt-4 mt-2 md:mt-5">
                <input
                  type="text"
                  id="VoucherCode"
                  className="font-inherit w-full border-0 border-b border-gray-400 outline-none text-sm text-gray-700 py-2 bg-transparent transition-colors duration-200 focus:pb-1.5 focus:border-b-2 focus:border-gray-700 peer"
                  value={voucherCode}
                  onChange={(e) => setVoucherCode(e.target.value)}
                  placeholder=" "
                  autoComplete="off"
                />
                <label
                  htmlFor="VoucherCode"
                  className="absolute left-0 top-0 block transition-all duration-200 text-xs text-gray-400 peer-placeholder-shown:text-sm peer-placeholder-shown:top-5 peer-placeholder-shown:cursor-text peer-focus:top-0 peer-focus:text-xs peer-focus:text-gray-700"
                >
                  Voucher Code
                </label>
              </div>

              {/* Contact Info Field */}
              <div className="relative pt-4 mt-2 md:mt-5">
                <input
                  type={inputType}
                  id="contactInfo"
                  className="font-inherit w-full border-0 border-b border-gray-400 outline-none text-sm text-gray-700 py-2 bg-transparent transition-colors duration-200 focus:pb-1.5 focus:border-b-2 focus:border-gray-700 peer"
                  value={contactInfo}
                  onChange={(e) => setContactInfo(e.target.value)}
                  placeholder=" "
                  autoComplete="off"
                />
                <label
                  htmlFor="contactInfo"
                  className="absolute left-0 top-0 block transition-all duration-200 text-xs text-gray-400 peer-placeholder-shown:text-sm peer-placeholder-shown:top-5 peer-placeholder-shown:cursor-text peer-focus:top-0 peer-focus:text-xs peer-focus:text-gray-700"
                >
                  {label}
                </label>
              </div>

              {/* Privacy Policy Checkbox */}
              <div className="relative pt-4 mt-2 text-left text-xs text-gray-400">
                <input
                  type="checkbox"
                  id="privacyPolicy"
                  name="privacyPolicy"
                  checked={privacyPolicy}
                  onChange={() => setPrivacyPolicy(!privacyPolicy)}
                  className="m-0 mr-1 scale-110 md:scale-120"
                />
                I agree to{" "}
                <a
                  href="#"
                  className="text-gray-500 no-underline hover:text-red-600 hover:no-underline transition-colors duration-300"
                >
                  privacy policy
                </a>
              </div>

              {/* Error Message */}
              {error && (
                <p className="text-red-600 text-xs text-left block mt-1.5">
                  {error}
                </p>
              )}

              {/* h-captcha hidden */}
              <div
                className="hidden text-center mt-10"
                data-sitekey="194e8efb-f32d-4d44-8ffb-b54694c2f321"
              ></div>

              {/* Submit Button */}
              <button
                type="submit"
                className="bg-gray-700 text-white no-underline px-7 text-center text-sm inline-block h-[38px] leading-[36px] mt-10 mb-8 md:mb-30 border-none w-auto rounded-full cursor-pointer transition-all duration-500 ease-in-out hover:bg-red-400 hover:text-white sm:max-w-[200px] sm:min-w-[150px] sm:px-6 sm:mt-7 sm:mb-7 xs:max-w-[180px] xs:min-w-[140px] xs:text-xs xs:h-[36px] xs:leading-[34px] xs:px-5"
                id="Signin"
              >
                Submit
              </button>
            </form>

            {/* Footer Links */}
            <div className="flex w-full justify-between mt-8 mb-16 text-center">
              <a
                href="#"
                className="w-1/3 flex flex-col items-center text-gray-500 no-underline hover:text-red-600 hover:no-underline transition-colors duration-300"
              >
                <svg
                  viewBox="0 0 24 24"
                  width="28"
                  height="28"
                  stroke="black"
                  strokeWidth="1"
                  fill="none"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="mb-1 transition-all duration-300 hover:stroke-red-600 sm:w-6 sm:h-6"
                >
                  <path d="M22.54 6.42a2.78 2.78 0 0 0-1.94-2C18.88 4 12 4 12 4s-6.88 0-8.6.46a2.78 2.78 0 0 0-1.94 2A29 29 0 0 0 1 11.75a29 29 0 0 0 .46 5.33A2.78 2.78 0 0 0 3.4 19c1.72.46 8.6.46 8.6.46s6.88 0 8.6-.46a2.78 2.78 0 0 0 1.94-2 29 29 0 0 0 .46-5.25 29 29 0 0 0-.46-5.33z"></path>
                  <polygon points="9.75 15.02 15.5 11.75 9.75 8.48 9.75 15.02"></polygon>
                </svg>
                <span className="text-xs mt-0 sm:text-[10px] sm:mt-1">User Guide</span>
              </a>
              <a
                href="#"
                className="w-1/3 flex flex-col items-center text-gray-500 no-underline hover:text-red-600 hover:no-underline transition-colors duration-300"
              >
                <svg
                  viewBox="0 0 24 24"
                  width="28"
                  height="28"
                  stroke="black"
                  strokeWidth="1"
                  fill="none"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="mb-1 transition-all duration-300 hover:stroke-red-600 sm:w-6 sm:h-6"
                >
                  <circle cx="12" cy="12" r="10"></circle>
                  <line x1="12" y1="8" x2="12" y2="16"></line>
                  <line x1="8" y1="12" x2="16" y2="12"></line>
                </svg>
                <span className="text-xs mt-0 sm:text-[10px] sm:mt-1">Add To Home Screen</span>
              </a>
              <a
                href="#"
                className="w-1/3 flex flex-col items-center text-gray-500 no-underline hover:text-red-600 hover:no-underline transition-colors duration-300"
              >
                <svg
                  viewBox="0 0 25 25"
                  width="28"
                  height="28"
                  stroke="black"
                  strokeWidth="1"
                  fill="none"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="mb-1 transition-all duration-300 hover:stroke-red-600 sm:w-6 sm:h-6"
                >
                  <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                  <polyline points="22,6 12,13 2,6"></polyline>
                </svg>
                <span className="text-xs mt-0 sm:text-[10px] sm:mt-1">Contact Us</span>
              </a>
            </div>
          </div>
        </div>

        {/* Left Column (Banner/Background) - Desktop Only */}
        <div
          className="hidden md:block flex-1 min-h-screen relative text-center bg-cover bg-repeat bg-center"
          style={{
            backgroundImage: `url(${
              data?.login_page_background || "/images/background.png"
            })`,
            backgroundColor: "#cccccc",
            backgroundSize: "500px 500px",
          }}
        >
          {data.login_page_banner && (
            <div className="absolute top-1/2 right-0 -translate-y-1/2 translate-x-[-55%] m-0">
              <img
                className="shadow-lg rounded-tl-2xl rounded-bl-2xl object-cover h-[85vh]"
                style={{ width: "155%" }}
                src={data.login_page_banner}
                alt="overlay background"
              />
            </div>
          )}
        </div>
      </div>
    </>
  );
}
