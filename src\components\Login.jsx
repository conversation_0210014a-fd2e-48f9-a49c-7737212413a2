import React, { useEffect, useState } from "react";
import "./css/Login.css";
import axios from "axios";
import { useNavigate } from "react-router-dom";
import CryptoJS from "crypto-js";
const SECRET_KEY = "your_secret_key";
import api from "../Api";
const encryptData = (data) => {
  return CryptoJS.AES.encrypt(data, SECRET_KEY).toString();
};

const decryptData = (encryptedData) => {
  const bytes = CryptoJS.AES.decrypt(encryptedData, SECRET_KEY);
  return bytes.toString(CryptoJS.enc.Utf8);
};

export default function Login() {
  const [voucherCode, setVoucherCode] = useState("");
  const [contactInfo, setContactInfo] = useState("");
  const [privacyPolicy, setPrivacyPolicy] = useState(false);
  const [error, setError] = useState("");
  const [data, setData] = useState("");
  const navigate = useNavigate();

  // call program setting  api

  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const v = params.get("v");
    const p = params.get("p");
    localStorage.setItem("p", p);
    {
      v && setVoucherCode(v);
    }

    const fetchData = async () => {
      try {
        const response = await api.get(
          `${
            import.meta.env.VITE_API_URL
          }/api/auth/get_program_settings?pid=${p}`
        );
        {
          response.status === 200 && setData(response.data.data.settings);
        }
        // console.log(response);
        //   const data = await response.json();
        //   setApiData(data);
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };

    fetchData();
  }, []);

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!voucherCode) {
      setError("Please Enter a valid Voucher Code");
      return;
    }
    const encryptedToken = encryptData(voucherCode);
    localStorage.setItem("token", encryptedToken);

    if (!contactInfo) {
      if (data.voucher_link_authentication_mode === 3) {
        setError("Please Enter Your Pin");
        return;
      } else if (data.voucher_link_authentication_mode === 2) {
        setError("Please Enter  Your  Email");
        return;
      } else {
        setError("Please Enter Your Mobile Number");
        return;
      }
    }

    let contactFieldName = "mobile";
    // data.voucher_link_authentication_mode
    switch (data.voucher_link_authentication_mode) {
      case 2:
        contactFieldName = "email";
        break;
      case 3:
        contactFieldName = "pin";
        break;
      default:
        contactFieldName = "mobile";
    }

    const formData = {
      voucher_code: voucherCode,
      [contactFieldName]: contactInfo,
    };

    try {
      const response = await axios.post(
        `${import.meta.env.VITE_API_URL}/api/auth/login`,
        formData
      );

      if (response.status === 200) {
        const tokenData = response.data?.data?.tokenData;
        console.log(tokenData);

        if (tokenData) {
          localStorage.setItem("tx", tokenData.tokenExpiry);
          localStorage.setItem("td", tokenData.token);

          if (response.data.data.link_token === "1") {
            navigate(`/mono_brand_voucher`);
          } else if (response.data.data.link_token === "2") {
            navigate(`/multi_brand_voucher`);
          }
        } else {
          navigate("/otp", {
            state: {
              userId: response.data.data.user_id,
              data: contactInfo,
              style: data,
              link_token: response.data.data.link_token,
            },
          });
        }
      }
    } catch (error) {
      setError(error.response.data.message);
      console.log("Error fetching data:", error);
    }
  };

  // field acc to auth mode

  const getInputDetails = () => {
    switch (data.voucher_link_authentication_mode) {
      case 1:
        return {
          inputType: "tel",
          label: "Mobile Number",
          placeholder: "Enter Mobile Number",
        };
      case 2:
        return {
          inputType: "email",
          label: "Email Address",
          placeholder: "Enter Email Address",
        };
      case 3:
        return {
          inputType: "text",
          label: "PIN",
          placeholder: "Enter PIN",
        };
      default:
        return {
          inputType: "text",
          label: "Contact Information",
          placeholder: "Enter Information",
        };
    }
  };
  const { inputType, label, placeholder } = getInputDetails();

  return (
    <>
      <div className="columnsContainer">
        <div className="mobile_image">
          <img width="100%" src={data.login_page_banner} alt="background" />
        </div>
        <div className="rightColumn">
          <div className="rg_logo">
            <span style={{ marginTop: "-30px", marginRight: "5px" }}>
              Powered By
            </span>
            <img
              src="./images/rg.png"
              height="25"
              style={{ marginBottom: "0px" }}
              alt="powered-by"
            />
          </div>
          <div
            id="DivLogin"
            style={{ position: "relative", textAlign: "center" }}
          >
            <div
              style={{ textAlign: "left", marginBottom: 0, marginLeft: "-5px" }}
            >
              <img width="55px" src="./images/2883833.png" alt="icon" />
            </div>

            <form onSubmit={handleSubmit}>
              <div className="form__group">
                <input
                  type="text"
                  id="VoucherCode"
                  className="form__field"
                  value={voucherCode}
                  onChange={(e) => setVoucherCode(e.target.value)}
                  placeholder=""
                />
                <label htmlFor="VoucherCode" className="form__label label">
                  Voucher Code
                </label>
              </div>

              <div className="form__group">
                <input
                  type={inputType}
                  id="contactInfo"
                  className="form__field"
                  value={contactInfo}
                  onChange={(e) => setContactInfo(e.target.value)}
                  placeholder={placeholder}
                />
                <label htmlFor="contactInfo" className="form__label label">
                  {label}
                </label>
              </div>

              <div
                className="form__group"
                style={{
                  marginTop: "10px !important",
                  textAlign: "left",
                  fontSize: "13px",
                  color: "#aaa",
                }}
              >
                <input
                  type="checkbox"
                  id="privacyPolicy"
                  name="privacyPolicy"
                  checked={privacyPolicy}
                  onChange={() => setPrivacyPolicy(!privacyPolicy)}
                  style={{
                    margin: 0,
                    marginRight: "5px",
                  }}
                />
                I agree to{" "}
                <a href="#" className="footer-link">
                  privacy policy
                </a>
              </div>

              {error && (
                <p
                  className=" small"
                  style={{
                    color: "red",
                    textAlign: "left",
                    display: "block",
                    marginTop: "6px",
                  }}
                >
                  {error}
                </p>
              )}

              <div
                className="h-captcha text-center mt4"
                style={{ display: "none" }}
                data-sitekey="194e8efb-f32d-4d44-8ffb-b54694c2f321"
              ></div>

              <button type="submit" className="btn" id="Signin">
                Submit
              </button>
            </form>

            <div style={{ marginBottom: "70px" }}>
              <a href="#" style={{ width: "33%", float: "left" }}>
                <svg
                  viewBox="0 0 24 24"
                  width="28"
                  height="28"
                  stroke="black"
                  strokeWidth="1"
                  fill="none"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="css-i6dzq1"
                >
                  <path d="M22.54 6.42a2.78 2.78 0 0 0-1.94-2C18.88 4 12 4 12 4s-6.88 0-8.6.46a2.78 2.78 0 0 0-1.94 2A29 29 0 0 0 1 11.75a29 29 0 0 0 .46 5.33A2.78 2.78 0 0 0 3.4 19c1.72.46 8.6.46 8.6.46s6.88 0 8.6-.46a2.78 2.78 0 0 0 1.94-2 29 29 0 0 0 .46-5.25 29 29 0 0 0-.46-5.33z"></path>
                  <polygon points="9.75 15.02 15.5 11.75 9.75 8.48 9.75 15.02"></polygon>
                </svg>
                <p className="small mt0 footer-link">User Guide</p>
              </a>
              <a href="#" style={{ width: "33%", float: "left" }}>
                <svg
                  viewBox="0 0 24 24"
                  width="28"
                  height="28"
                  stroke="black"
                  strokeWidth="1"
                  fill="none"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="css-i6dzq1"
                >
                  <circle cx="12" cy="12" r="10"></circle>
                  <line x1="12" y1="8" x2="12" y2="16"></line>
                  <line x1="8" y1="12" x2="16" y2="12"></line>
                </svg>
                <p className="small mt0 footer-link">Add To Home Screen</p>
              </a>
              <a href="#" style={{ width: "33%", float: "left" }}>
                <svg
                  viewBox="0 0 25 25"
                  width="28"
                  height="28"
                  stroke="black"
                  strokeWidth="1"
                  fill="none"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="css-i6dzq1"
                >
                  <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                  <polyline points="22,6 12,13 2,6"></polyline>
                </svg>
                <p className="small mt0 footer-link">Contact Us</p>
              </a>
            </div>
          </div>
        </div>
        <div
          className="leftColumn"
          style={{
            backgroundImage: `url(${
              data?.login_page_background || "/images/background.png"
            })`,
          }}
        >
          {data.login_page_banner && (
            <div className="overlay">
              <img
                width="155%"
                src={data.login_page_banner}
                alt="overlay background"
              />
            </div>
          )}
        </div>
      </div>
    </>
  );
}
