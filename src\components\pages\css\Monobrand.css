ol {
    
    padding-inline-start: 15px;
    line-height: 180%;
    
}

ul {
    position: relative;
    list-style: none;
    margin-left: 0;
    padding-left: 1.2em;
    padding-inline-start: 20px;
    line-height: 180%;
    
}
ul li:before {
    content: "✔";
    position: absolute;
    left: 0;    
}
.faq > li{
    font-weight: 500;
    color: black;
}
.faq > p{
    font-size: 13px;
    margin: 7px 0;
}

/* scrollbar */
::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}

::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  -webkit-border-radius: 10px;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  -webkit-border-radius: 10px;
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.3);
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.5);
}

::-webkit-scrollbar-thumb:window-inactive {
  background: rgba(255, 255, 255, 0.3);
}



.columnsContainer_mono,
footer,
header {
	position: relative;
	max-width: 1000px;
	margin-left:auto;
	margin-right: auto;
	
}
button,
input,
select {
    /* -webkit-appearance: none; */
    outline: 0
}

.btn,
button,
select {
    cursor: pointer
}

a {
    text-decoration: none;
    color: #666
}

a:hover {
    text-decoration: none;
    color: #0076b6
}

.small {
    font-size: 13px;
}

.color-black {
    color: black;
}

.color-theme {
    color: #0076b6
}

.row {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;   
}

.mt0 {
    margin-top: 0;
}
.mt1 {
    margin-top: 10px;
}
.mt2 {
    margin-top: 20px;
}
.mt3 {
    margin-top: 30px;
}
.mt4 {
    margin-top: 40px;
}
.mt5 {
    margin-top: 50px;
}


.mb0 {
    margin-bottom: 0;
}

.mb1 {
    margin-bottom: 10px;
}

.mb2 {
    margin-bottom: 20px;
}
.mb3 {
    margin-bottom: 30px;
}

.mb4 {
    margin-bottom: 40px;
}

.actionbg,
.btn,
button {
    border-radius: 5px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -o-border-radius: 5px;
    -ms-border-radius: 5px
}

.btn:hover,
button:hover {
    transition: .5s ease;
    -webkit-transition: .5s ease;
    -o-transition: .5s ease;
    -ms-transition: .5s ease;
    -moz-transition: .5s ease;
    background: #001b92;
    color: #fff
}

.btn {
    background: #0076b6;
    color: #fff;
    text-decoration: none;
    padding: 0 20px;
    text-align: center;
    font-size: 12px;
    display: inline-block;
    height: 32px;
    line-height: 32px;
    margin-top: 10px;
    margin-bottom: 5px;
    border: none
}


.leftColumn_mono,
.rightColumn_mono,
footer,
header {
    padding: 1.25em
}

.ls1 {
    letter-spacing: 1px;
}

.leftColumn_mono {  
    text-align: left;
    box-sizing: border-box; 
    font-size: 13px;
    color: #666666;
    line-height: 25px;
    position: relative; 
    width: 50%; 
}


.rightColumn_mono {
    text-align: center; 
    
    background-color: white;
    
    background-color: #ffffff;  
    background-size: contain;
    background-repeat: no-repeat;
    background-position:bottom;
    margin-left: auto;
    margin-right: auto;
    position:relative;  
}

.rightColumn-envelop{
    text-align: center; 
    padding: 3em 4em 4em 4em;
    background-color: white;
    background-image: url("./images/envelop_bg.jpg");     
    background-color: #ffffff;  
    background-size: contain;
    background-repeat: no-repeat;
    background-position:bottom;
    margin-left: auto;
    margin-right: auto;
    position:relative;  
    width: 95%;
}

.content-3 {
    line-height: 25px;
    color: #666666;
    padding: 0 2em 2em 2em;
}

.brand {
    margin-bottom: -70px; 
    z-index:2; 
    position:sticky;
    width: 75%;
    min-width: 200px;
    
}

.code_label {
    width:50px; 
    display:block; 
    float:left; 
    color:#555555; 
    border-right:1px solid #FFFFFF;
    
}

.code {
    
    color:black; 
    letter-spacing: 1px;
    font-weight: 600;
    font-size: 1.1rem;
    position: absolute;
    left: 0;
    right: 0;
    
}
 .copy_s {
    float:right; 
    border-radius: 20px; 
    padding:3px; 
    background-color: white;
    border-color: #ECECEC;
    margin-right: 7px;
    cursor: hand;
    position: relative;
    
}

/* .copy_pin {
    margin-left: 10px; 
    border-radius: 20px; 
    border: 1px solid #eee; 
    background-color: #eee; 
    padding: 7px; 
    width: 30px; 
    display: inline-block;
    cursor: hand;
} */
         
#loader{
    background-color: #FFF;
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right:0;
    z-index: 2999;
    text-align: center;
}



.coupon_p1 {
    background-color: #FFFFFF;  
    border: 1px solid #dddddd;
    filter: drop-shadow(0 3px 5px rgba(0, 0, 0, 0.1));
    border-bottom: 0;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px; 
    min-height: 290px;
    overflow: hidden;
    padding-top:70px;
}

.coupon_p2_s {
    background-color: #ECECEC;  
    border-radius: 7px;
    z-index: 3;
    height: 43px;
    margin-left: -20px;
    margin-right: -20px;
    padding: 7px;
    line-height: 25px;
    border: 1px solid #dddddd;
}

.coupon_p3_s {  
    border-bottom-left-radius: 10px;    
    height: 105px;
    width: 4%;
    float:left; 
    background-image: radial-gradient( circle at 0 40%, transparent 10px, #fff 11px );

    filter: drop-shadow(0 0px 1px rgba(0, 0, 0, 0.2));
}

.coupon_p4_s {
    background-color: #FFFFFF;
    position: sticky;
    z-index: 3;
    height: 105px;
    width: 92%;
    float:left; 
    overflow: hidden;
    text-align: center; 
    padding-top:6px;
    filter: drop-shadow(0 1px 0px rgba(0, 0, 0, 0.1));
}

.coupon_p5_s {      
    border-bottom-right-radius: 10px;   
    height: 105px;
    width: 4%;
    float:left;
    background-image: radial-gradient( circle at 100% 40%, transparent 10px, #fff 11px );   

    filter: drop-shadow(0 0px 1px rgba(0, 0, 0, 0.2));
}


.float-child {    
    float: left;
} 

.float-child-left {
    width: 5px;
    margin-right: 15px; 
    background-color: #CDBB9B; 
    border-radius: 5px;
} 

.float-child-right {
    width: 90%;
    padding: 10px 5px;    
}  


@media screen and (min-width:786px) {
    .leftColumn_mono {       
        min-height: 100vh;
        padding: 50px 30px 0 30px;
    }

    .rightColumn_mono {
        position: absolute;
        top: 0;
        right: 0;
        width: 50%;
        height: 100vh;
        min-height: 640px;
    }

    .mobile_image {
        display: none;
    }

    .content-3 {
        display: none;      
    }
}

@media screen and (max-width:992px) {
    .rightColumn_mono {
        padding: 1em 0em;
    }
    .rightColumn-envelop {
        padding: 3em 3em 4em 3em;
    }
    .code {
        letter-spacing: 1px;
        font-weight: 600;
        font-size: 1rem;
    }
    
}


@media screen and (max-width:786px) {
    .leftColumn_mono {
        width: 100%;
        padding: 2em;
        padding-bottom: 0;
        padding-top: 1em;
    }

    .content-2 {
        display: none;
    }

    
    .rightColumn-envelop {
        padding: 0em 3em 1.5em 3em;
    }
    .code {
        letter-spacing: 0px;        
        font-size: 1rem;
    }
}

@media screen and (max-width:350px) {
    .coupon_p2 {
        background-color: #ECECEC;  
        border-radius: 7px;
        /* <!-- position: sticky; --> */
        z-index: 3;
        height: 43px;
        margin-left: -45px;
        margin-right: -45px;
        padding: 7px;
        line-height: 25px;
        border: 1px solid #dddddd;
    }
}


@media screen and (min-width:992px) {   
    .rightColumn_mono {
        position: absolute;
        top: 0;
        right: 0;
        width: 45%;
    }
    .rightColumn-envelop {
        position: absolute;
        top: 0;
        right: 0;
        
    }
}




.btnTab {
  font-size: 12px;
  font-weight: 600;
  padding: 10px;
  background: transparent;
  outline: transparent;
  border: transparent;
  transition: 300ms all ease-in-out;
}
@media only screen and (max-width: 576px) {
  .btn {
    padding: 0px 8px;
  }
}
.btnTab.active {
  color: #2955ac;
  position: relative;
  
}

.btnTab:hover {  
  background-color: #fff;
  color: #2955ac;
}
.btnTab.active::after {
  content: "";
  width: 100%;
  height: 2px;
  background-color: #2955ac;
  position: absolute;
  bottom: -2px;
  left: 0;
  color: #2955ac;
}

.tabs {

  padding: 0px;
}
.tabs__pills {
  width: fit-content;
  border-bottom: 2px solid rgba(16, 25, 39, 0.2);
}

.tabs__panels > div {
  width: 100%;
  display: none;
  gap: 20px;
}
.tabs__panels > div > figure {
  max-width: 400px;
  width: 100%;
  flex-shrink: 0;
}
@media only screen and (max-width: 992px) {
  .tabs__panels > div > figure {
    margin: 0 auto;
  }
}
.tabs__panels > div > figure img {
  width: 100%;
  display: block;
}
.tabs__panels > div > div > h4 {
  font-size: 24px;
  margin: 10px 0;
}
.tabs__panels > div > div > p {
  line-height: 1.8;
}
.tabs__panels > div.active {
  display: flex;
}
@media only screen and (max-width: 978px) {
  .tabs__panels > div.active {
    flex-direction: column;
  }
}






.main-content .description-title {
  font-size: 16px;
  font-weight: bold;
  background-color: #fff;
  color: #0076b6;
  padding-left: 10px;
  line-height: 45px;
  border-radius: 5px;
  transition: 0.3s;
  margin-top: 2px;
  margin-bottom: 2px;
  border-bottom: 1px solid #eeeeee;
}
.main-content .description-title:hover {
  background-color: #eeeeee;
  cursor: pointer;
}
.main-content .expand-collapse {
  float: right;
  margin-right: 8px;
  color: #35353f; 
}
.main-content .description {
  font-size: 18px;
  color: #35353f;
  max-height: 0;
  overflow: hidden;
  margin-left: 0px;
  padding-left: 10px;
  transition: max-height 0.2s ease-out;
}
.main-content .description p {
  margin-top: 4px;
}












.accordion__trigger {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  font-size: 15px;
  font-weight: 500;
  /*   text-transform: uppercase; */
  letter-spacing: 1px;
  padding: 0.7rem 0.4rem;
  background: #fff;
  color: #0076b6;
  cursor: default;
  transition: 0.3s ease;
  border: none;
  border-bottom: 1px solid #eee;
  width: 100%;
  text-align: left;
  margin: 0;    
  position: relative;
}
.accordion__trigger::after {
  content: "";
  position: absolute;
  right: 20px;
  top: calc(50% - 5px);
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-top: 10px solid #ddd;
  transform: rotate(0deg);
  transform-origin: center;
  transition: transform 0.5s;
}
.accordion__trigger[aria-expanded="true"]::after {
  transform: rotate(180deg);
}
.accordion__trigger:hover,
.accordion__trigger:focus {
  background: #fff;
  color: #0076b6;
}

.accordion__copy {
  overflow: hidden;
  overflow-y: auto;
  padding: 0 5px;
  color: #666;
  line-height: 1.6;
  font-size: 1rem;
  font-weight: 400;
  display: none;
  /* height: 0; */
  /* visibility: hidden; */
  transition: visibility 0.5s, padding 0.5s, max-height 0.5s;
}

.accordion__trigger[aria-expanded="true"] + .accordion__copy {
	display: block;
  }
.accordion__copy--open {
  visibility: visible;
  height: auto !important;
}
.accordion__copy__pic {
  width: 100px;
  height: auto;
  float: right;
  margin: 0 0 0 20px;
}
@media (min-width: 540px) {
  .container__head {
    font-size: 2rem;
  }
  .accordion__copy__pic {
    width: 180px;
  }
}

.accordion__heading{
    margin-bottom: 2px;
}

.card{
    width:48%;
    border: 1px solid #eee;
    border-radius: 5px;
    font-weight: 600;
    margin: 1%;
    cursor: hand;
    
}
#offer{
    border: 1px solid #FFF8DC; 
    border-radius: 5px; 
    padding: 10px 15px; 
    background: linear-gradient(to bottom, #FFF8DC 0%, #ffffff 100%);
}








.leftColumn_mono,
.rightColumn_qty,
footer,
header {
    padding: 1.25em
}

.ls1 {
    letter-spacing: 1px;
}

.leftColumn_mono {   
    text-align: left;
    box-sizing: border-box; 
    font-size: 13px;
    color: #666666;
    line-height: 25px;
    position: relative; 
    width: 50%; 
}


.rightColumn_qty {
    text-align: center; 
    padding: 5em 4.5em 0 4.5em;
    background-color: white;
    background-color: #ffffff;  
    background-size: 100% 68%;
    background-repeat: no-repeat;
    background-position:bottom;
    margin-left: auto;
    margin-right: auto;
    position:relative;  
}

.content-3 {
    line-height: 25px;
    color: #666666;
    padding: 2em;
}

.brand {
    margin-bottom: -70px; 
    z-index:2; 
    position:sticky;
    min-width: 200px;
}

.code_label {
    width:50px; 
    display:block; 
    float:left; 
    border-right:1px solid #FFFFFF;
}

.code {
    min-width:200px; 
    float:left; 
    color:black; 
    letter-spacing: 1px;
}
.copy {
    float:right; 
    border-radius: 20px; 
    padding:2px; 
    background-color: white;
    border-color: #ECECEC;
}
.copy>a{
    position: relative;
  z-index: 2;
}

.copy_pin {
    margin-left: 10px; 
    border-radius: 20px; 
    border: 1px solid #eee; 
    background-color: #ffffff; 
    padding: 7px; 
    width: 30px; 
    display: inline-block;
}


.coupon_p1 {
    background-color: #FFFFFF;  
    border: 1px solid #dddddd;
    filter: drop-shadow(0 3px 5px rgba(0, 0, 0, 0.1));
    border-bottom: 0;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px; 
    min-height: 330px;
    overflow: hidden;
    padding-top:70px;
}

.coupon_p1_modal {
    background-color: #FFFFFF;  
    border: 1px solid #dddddd;
    filter: drop-shadow(0 3px 5px rgba(0, 0, 0, 0.1));
    border-bottom: 0;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px; 
    min-height: 315px;
    overflow: hidden;
    padding-top:10px;
    
}

.coupon_p2 {
    background-color: #ffffff;  
    
    position: sticky;
    z-index: 3;
    height: 10px;
    
    padding: 7px;
    line-height: 10px;
}

.coupon_p2_modal {
    background-color: #e0e0e0;  
    border-radius: 7px;
    position: sticky;
    z-index: 3;
    height: 40px;
    margin-left: -20px;
    margin-right: -20px;
    padding: 7px;
    line-height: 20px;
}

.coupon_p3 {    
    border-bottom-left-radius: 10px;    
    height: 80px;
    width: 4%;
    float:left; 
    background-image: radial-gradient( circle at 0 17%, transparent 10px, #fff 11px );
}

.coupon_p4 {
    background-color: #FFFFFF;
    position: sticky;
    z-index: 3;
    height: 80px;
    width: 92%;
    float:left; 
    overflow: hidden;
    text-align: center; 
    padding-top:6px; 
}

.coupon_p5 {        
    border-bottom-right-radius: 10px;   
    height: 80px;
    width: 4%;
    float:left;
    background-image: radial-gradient( circle at 100% 17%, transparent 10px, #fff 11px );   
}

.coupon_p3_modal {  
    border-bottom-left-radius: 10px;    
    height: 105px;
    width: 4%;
    float:left; 
    background-image: radial-gradient( circle at 0 40%, transparent 10px, #fff 11px );
}

.coupon_p4_modal {
    background-color: #FFFFFF;
    position: sticky;
    z-index: 3;
    height: 105px;
    width: 92%;
    float:left; 
    overflow: hidden;
    text-align: center; 
    padding-top:6px; 
}

.coupon_p5_modal {      
    border-bottom-right-radius: 10px;   
    height: 105px;
    width: 4%;
    float:left;
    background-image: radial-gradient( circle at 100% 40%, transparent 10px, #fff 11px );   
}


.float-child {    
    float: left;
} 

.float-child-left {
    width: 5px;
    margin-right: 15px; 
    background-color: #CDBB9B; 
    border-radius: 5px;
} 

.float-child-right {
    width: 90%;
    padding: 10px 5px;    
}  


@media screen and (min-width:786px) {
    .leftColumn_mono {       
        min-height: 100vh;
        padding: 50px 30px 0 30px;
    }

    .rightColumn_qty {
        position: absolute;
        top: 0;
        right: 0;
        width: 50%;
        height: 100vh;
        min-height: 640px;
    }

    .mobile_image {
        display: none;
    }

    .content-3 {
        display: none;      
    }
}

@media screen and (max-width:992px) {
    .rightColumn_qty {
        padding: 3em 3em 2em 3em;
    }
}


@media screen and (max-width:786px) {
    .leftColumn_mono {
        width: 100%;
        padding: 2em;
        padding-bottom: 0;
        padding-top: 1em;
    }

    .content-2 {
        display: none;
    }

    .rightColumn {
        padding: 1em 2em 2em 2em;
    }
}

@media screen and (min-width:992px) {   
    .rightColumn_qty {
        position: absolute;
        top: 0;
        right: 0;
        width: 45%;
    }
}

/* Modal */

.modal {
    position: fixed;
    width: 100vw;
    height: 100vh;
    
    transition: all 0.3s ease;
    top: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 4;
  }
  .modal.open {
    visibility: visible;
    opacity: 1;
    transition-delay: 0s;
  }
  .modal-bg {
    position: absolute;
    background: rgba(0, 0, 0, 0.7);
    width: 100%;
    height: 100%;
  }
  .modal-container {
    border-radius: 10px;
    background: #ffffff00;
    position: relative;
    padding: 30px;
    text-align: center;
    width: 335px;
  }
  .modal-close {
    position: absolute;
    right: 15px;
    top: 15px;
    outline: none;
    appearance: none;
    color: red;
    background: none;
    border: 0px;
    font-weight: bold;
    cursor: pointer;
  }
  .close_modal{
      text-align: right;
      margin-right: 10px;
      cursor: pointer;
  }
  
  .voucherNo {
      color: #0076b6;
  }
/* Modal */



.voucherContainer {
  display: flex;
  margin: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  
}
.voucherContainerdiv1 {
  float:left; /*grow*/
  width: 20%;
  padding: 10px;
  color: white;
  background-color: #0076b6;
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;
  font-size: 14px;
}
.voucherContainerdiv2 {
  float:left; /*grow*/
  width: 68%;
  padding: 10px;
  background-color: #f1f1f1;
  color: #000000;
}

.voucherContainerdiv2:hover {
  float:left; /*grow*/
  width: 68%;
  padding: 10px;
  background-color: #f1f1f1;
  color: #0076b6;
  font-weight: bold;
}

.voucherContainerdiv3 {
  float:left; /*grow*/
  width: 15%;
  background-color: #f1f1f1;
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;

}
.voucherContainerdiv3 > img{
    width:30px;
    padding: 10px 5px;
    background-color: #f1f1f1;
    
}

.notViewed{
    width:30px !important;
    padding: 5px !important;
    margin: 5px !important;
    background-color: #f1f1f1;
    opacity: 0.3;
}


.toast {
	position: fixed;
	top: 20px;
	left: 50%;
	transform: translateX(-50%);
	background-color: rgba(0, 0, 0, 0.8);
	color: white;
	padding: 10px 20px;
	border-radius: 5px;
	font-size: 16px;
	font-weight: bold;
	z-index: 9999;
	opacity: 0;
	visibility: hidden; 
	transition: opacity 0.3s ease, visibility 0.3s ease; 
  
	&.show {
	  opacity: 1;
	  visibility: visible;
	}
  }
  
  @keyframes fadeInOut {
	0% {
	  opacity: 0;
	  top: 0;
	}
	10% {
	  opacity: 1;
	  top: 20px;
	}
	90% {
	  opacity: 1;
	  top: 20px;
	}
	100% {
	  opacity: 0;
	  top: 0;
	}
  }
  
  
