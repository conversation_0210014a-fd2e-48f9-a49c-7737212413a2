:root {
    --theme: #a80100;
    --themesecondary: #004C8F;
    --white: #ffffff;
  }
  
  /* breakpoint and down - styles  Theme color: f9a51a   */
  @media screen and (max-width: 768px) {
      
      .sidebar-wrapper .sidebar-header {	 
        background-color: var(--theme);
        color: #FFFFFF;
      }
  
      .fullscreenModal .modal-dialog .modal-header {  		
          background-color:var(--theme);
          color: #FFFFFF;
      }
      
      .fullscreenModal .modal-dialog .modal-footer {  
          /* background-color:var(--theme);
          color: #FFFFFF; */
      }
  
  }
  
  
  @media screen and (max-width: 991px) {
      .sidebar-wrapper .sidebar-header {
          background-color:var(--theme);
      }
  }	
  
  .text-theme {    
      color: var(--theme) !important;
  }
  .text-theme-dark {    
      color: #de8d07 !important;
  }
  
  .text-theme-light {    
      color: #ffce2b !important;
  }
      
  
  .bg-theme {
      background-color: var(--theme)!important;
      color: #FFF;
  }
  
  .bg-theme-secondary {
      background-color: var(--themesecondary)!important;
      color: #FFF;
  }
  
  
  
  
  .bg-theme-dark {
      background-color: #de8d07!important;
      color: #FFF;
  }
  .bg-theme-light {
      background-color: #ffce2b!important;
      color: #FFF;
  }
  
  
  
  .btn-theme {
      color: #fff;
      background-color: var(--theme);
      border-color: var(--theme);
  }
  
  .btn-theme:hover {
      color: #fff;
      box-shadow: 0px 2px 5px rgba(0,0,0,0.5);	
  }
  
  
  .btn-theme-secondary {
      color: #fff;
      background-color: var(--themesecondary);
      border-color: var(--themesecondary);
  }
  
  .btn-outline-theme {
      color: var(--theme);
      border-color: var(--theme);
      background-color: #FFF;
  }
  
  
  .btn-hover.color-11 {      
      background-color: var(--theme);	  
      color: #fff;
  }
  
  .list-group-item.active {  
      color: #fff;
      background-color: var(--theme);
      border-color: var(--theme);
  }
  
  .nav-tabs .nav-item.show .nav-link, .nav-tabs .nav-link.active {
      color: var(--theme);
      font-weight: 500;
      
  }
  
  
  
  .filterbox .card-header .btn {
      color: var(--theme);
  }
      
  .filterbox .card-header .btn:hover{
      color: var(--theme);
  }
  
  .radio .helper::after {
    background-color: var(--theme);
    border-color: var(--theme);
  }
  .radio label:hover .helper {
    color: var(--theme);
  }
  
  .radio input:checked ~ .helper::before {
    color: var(--theme);
  }
  
  .checkbox .helper::before, .checkbox .helper::after {
    background-color: var(--theme); 
  }
  
  .text-theme{
      color:var(--theme)!important;
  }
  
  
  .link:hover{
      color: var(--theme)!important;
  }
  
  
  .swiper-pagination-progressbar .swiper-pagination-progressbar-fill {
      background: var(--theme);
  }
  
  [slider] > div > [range] {
    background-color: var(--theme);
  }
  
  #clockdiv div > span{
      color: var(--theme);	
  }
  
  
  #campaign-list.nav-tabs .nav-item.show .nav-link, #campaign-list.nav-tabs .nav-link.active {
      color: var(--theme);
      background-color: #FFF;
      border-top: 0px solid var(--theme);	
  }
  
  
  .stepwizard-step .active{
      color: #fff !important;
      background-color: var(--theme) !important;
      border-color: var(--theme) !important;
  }
  
  
  
  .header-mobile .dropdown .dropdown-menu .admin {    
      background-color: var(--theme);
      color: #FFF;
  }
  
  #campaign-list.nav-tabs .nav-item.show .nav-link, #campaign-list.nav-tabs .nav-link.active {	
      border-bottom: 3px solid var(--theme);	
  }
  
  
  
  @media screen and (min-width: 992px) {
      .navbar-light .navbar-nav .active>.nav-link{		
          background-color: var(--theme);
          color: #FFF;
      }
  }
  
  
  
  
  .noUi-horizontal .noUi-handle, .noUi-vertical .noUi-handle {
      background: var(--theme);
  }
  
  .btn-secondary:not(:disabled):not(.disabled).active, .btn-secondary:not(:disabled):not(.disabled):active, .show>.btn-secondary.dropdown-toggle {
      color: #fff;
      background-color: var(--theme);
      border-color: var(--theme);
  }
  
  
  
  
  
  
  .stepwizard-active{	
      
      font-weight: 500;
      color: #FFF;
  }
  
  .btn-outline-theme {
      color: var(--theme);
      background-color: transparent;
      background-image: none;
      border-color: var(--theme);
      transition: 0.3s;
  }
  
  .btn-outline-theme:hover {
      color: #FFF;
      background-color: var(--theme);
  }
  
  
  .page-item.active .page-link {
      
      color: #fff;
      background-color: var(--theme);
      border-color: var(--theme);
  }
  
  .page-link {
   
      color: #6c757d;
      background-color: #fff;
      border: 1px solid #dee2e6;
  }
  
  
  
  .wizard-card[data-color="red"] .moving-tab {
      background-color: var(--theme);
  }
  
  .wizard-footer .btn-finish {
      color: var(--theme);
  }
  
  
  .btn-primary:active,
  .btn-primary.active,
  .open > .dropdown-toggle.btn-primary {
      color: #fff;
      background-color: var(--theme);
      border-color: var(--theme);
  }
  .btn-outline-theme:not(:disabled):not(.disabled).active, .btn-outline-theme:not(:disabled):not(.disabled):active, .show>.btn-outline-theme.dropdown-toggle {
      color: #FFFFFF;
      background-color: var(--theme);
      border-color: var(--theme);
  }
  
  .nav-link.theme.active {
      color: #fff;
      background-color: var(--theme);
  }
  
  .theme-grey .btn-outline-theme {
      color: #999999;
      background-color: transparent;
      background-image: none;
      border-color: #999999;
      transition: 0.3s;
  }
  
  .theme-grey .btn-outline-theme:hover {
      color: #555555;
      background-color: #eeeeee;
      border-color: #999999;
  }