import { showSuccessToast,showInfoToast,showErrorToast} from './actions';


const initialState = {
  cart: [], 
  walletBalance: 0, 
  maxQuantity: 0,
  cartLength: 0, 
  totalAmount: 0, 
};

const cartReducer = (state = initialState, action) => {
  switch (action.type) {
    case "ADD_TO_CART": {
      const { productCode, quantity, amount, product } = action.payload;

      // Ensure quantity is valid
      if (quantity <= 0) return state;

      // If the product already exists in the cart, just update the quantity and amount
      const existingProduct = state.cart.find(
        (item) => item.product.product_code === productCode
      );

      if (existingProduct) {
        // Increase the quantity and update the amount
        const updatedQuantity = existingProduct.quantity + quantity;
        const updatedAmount = existingProduct.amount + amount;

        // Check if the new quantity exceeds max quantity
        if (updatedQuantity > state.maxQuantity) {
          alert(`Cannot increase quantity beyond the maximum limit of ${state.maxQuantity}.`);
          return state;
        }

        // Deduct the amount from wallet balance if sufficient balance is available
        const newWalletBalance = state.walletBalance - amount;
        if (newWalletBalance < 0) {
          showErrorToast("Insufficient wallet balance.");
          return state;
        }

        // Update the cart and calculate new cartLength and totalAmount
        const updatedCart = state.cart.map((item) =>
          item.product.product_code === productCode
            ? {
                ...item,
                quantity: updatedQuantity,
                amount: updatedAmount,
              }
            : item
        );

        // Calculate the totalAmount by summing up all item amounts in the cart
        const totalAmount = updatedCart.reduce((total, item) => total + item.amount, 0);

        return {
          ...state,
          cart: updatedCart,
          walletBalance: newWalletBalance,
          cartLength: updatedCart.length,
          totalAmount, // Update total amount
        };
      }

      // If the product doesn't exist, add it to the cart
      // Calculate the amount based on the initial quantity
      const initialAmount = amount * quantity;

      if (quantity > state.maxQuantity) {
        alert(`Cannot add more than ${state.maxQuantity} items.`);
        return state;
      }

      const newWalletBalance = state.walletBalance - initialAmount;
      if (newWalletBalance < 0) {
        showErrorToast("Insufficient wallet balance.");
        return state;
      }

      const updatedCart = [
        ...state.cart,
        {
          product, 
          quantity, 
          amount: initialAmount,
        },
      ];

      // Calculate the totalAmount by summing up all item amounts in the cart
      const totalAmount = updatedCart.reduce((total, item) => total + item.amount, 0);

      return {
        ...state,
        cart: updatedCart,
        walletBalance: newWalletBalance,
        cartLength: updatedCart.length,
        totalAmount, // Update total amount
      };
    }

    case "UPDATE_PRODUCT_QUANTITY": {
      const { productCode, updatedQuantity, updatedAmount } = action.payload;
// console.log(updatedAmount)
      const parsedUpdatedAmount = parseFloat(updatedAmount);
      if (isNaN(parsedUpdatedAmount)) {
        console.error("Invalid updated amount:", updatedAmount);
        return state;
      }

      const currentProduct = state.cart.find(
        (item) => item.product.product_code === productCode
      );

      if (!currentProduct) {
        console.error(`Product with code ${productCode} not found in cart.`);
        return state;
      }

      if (updatedQuantity > state.maxQuantity) {
        alert(`Cannot increase quantity beyond the maximum limit of ${state.maxQuantity}.`);
        return state; 
      }

      const previousAmount = currentProduct.amount;
      const differenceAmount = parsedUpdatedAmount - previousAmount;

      const parsedWalletBalance = parseFloat(state.walletBalance);
      if (isNaN(parsedWalletBalance)) {
        console.error("Invalid wallet balance.");
        alert("Invalid wallet balance.")
        return state;
      }

      const newWalletBalance = parsedWalletBalance - differenceAmount;

      if (newWalletBalance < 0) {
        showErrorToast("Insufficient wallet balance.");
        return state; 
      }

      const updatedCart = state.cart.map((item) =>
        item.product.product_code === productCode
          ? {
              ...item,
              quantity: updatedQuantity,
              amount: parsedUpdatedAmount, 
            }
          : item
      );

      // Calculate the totalAmount by summing up all item amounts in the cart
      const totalAmount = updatedCart.reduce((total, item) => total + item.amount, 0);

      return {
        ...state,
        cart: updatedCart,
        walletBalance: newWalletBalance,
        cartLength: updatedCart.length,
        totalAmount, // Update total amount
      };
    }

    case "REMOVE_PRODUCT_FROM_CART": {
      const productToRemove = state.cart.find(
        (item) => item.product.product_code === action.payload
      );

      if (!productToRemove) {
        console.error(`Product with code ${action.payload} not found in cart.`);
        return state;
      }

      const productAmount = parseFloat(productToRemove.amount);
      const currentWalletBalance = parseFloat(state.walletBalance);

      const newWalletBalanceAfterRemoval = currentWalletBalance + productAmount;

      const updatedCart = state.cart.filter(
        (item) => item.product.product_code !== action.payload
      );

      // Calculate the totalAmount by summing up all item amounts in the cart
      const totalAmount = updatedCart.reduce((total, item) => total + item.amount, 0);

      return {
        ...state,
        cart: updatedCart,
        walletBalance: newWalletBalanceAfterRemoval,
        cartLength: updatedCart.length,
        totalAmount, // Update total amount
      };
    }

    case "UPDATE_WALLET_BALANCE":
      return {
        ...state,
        walletBalance: action.payload,
      };

    case "UPDATE_MAX_QUANTITY":
      const updatedMaxQuantity = action.payload == null ? 1 : action.payload;
      return {
        ...state,
        maxQuantity: updatedMaxQuantity, 
      };

      case 'CLEAR_CART': 
      return {
        ...state,
        cart: [], 
        cartLength: 0,        
        totalAmount: 0,
      };

    default:
      return state;
  }
};

export default cartReducer;
