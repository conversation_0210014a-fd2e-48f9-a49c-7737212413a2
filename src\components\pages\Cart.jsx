import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  updateProductQuantity,
  removeProductFromCart,
  clearCart
  ,showErrorToast,updateWalletBalance
} from "../../redux/actions";
import api from "../../Api";
import OrderDetails from "./OrderDetails";
import CryptoJS from 'crypto-js';
const SECRET_KEY = 'your_secret_key';
const encryptData = (data) => {
  return CryptoJS.AES.encrypt(data, SECRET_KEY).toString();
};

const decryptData = (encryptedData) => {
  const bytes = CryptoJS.AES.decrypt(encryptedData, SECRET_KEY);
  return bytes.toString(CryptoJS.enc.Utf8); 
};


export default function Cart({ handleCartModalToggle, rt }) {
  const {
    redemption_type,
    voucher_delivery_mode,
    user,
    allow_email_change,
    allow_mobile_no_change,
  } = rt || {};
  const [isMobileChangeAllowed, setIsMobileChangeAllowed] = useState(false);
  const [isEmailChangeAllowed, setIsEmailChangeAllowed] = useState(false);
  const [cartModal, setCartModal] = useState(true);
  const [orderModal, setOrderModal] = useState(false);
  const [order, setOrder] = useState('');
  const [token,setToken]=useState('')

  useEffect(() => {
    setIsMobileChangeAllowed(allow_mobile_no_change === 1);
    setIsEmailChangeAllowed(allow_email_change === 1);
  }, [rt]);
  useEffect(() => {
    const encryptedToken = localStorage.getItem('token');
    if (encryptedToken) {
      const decryptedEmail = decryptData(encryptedToken);
      setToken(decryptedEmail);
    }
  }, []);
  const dispatch = useDispatch();
  const { cart, walletBalance, maxQuantity, totalAmount } =
    useSelector((state) => state.cart);

  const safeCart = cart || [];

  const handleIncreaseQuantity = (productCode, currentQuantity, faceValue) => {
    const parsedFaceValue = parseFloat(faceValue);
    if (isNaN(parsedFaceValue)) {
      console.error("Invalid face value:", faceValue);
      return;
    }

    const newQuantity = currentQuantity + 1;

    if (newQuantity > maxQuantity) {
      alert(
        `Cannot increase quantity beyond the maximum limit of ${maxQuantity}.`
      );
      return;
    }

    const updatedAmount = parsedFaceValue * newQuantity;

    const parsedWalletBalance = parseFloat(walletBalance);
    if (isNaN(parsedWalletBalance)) {
      alert("Invalid wallet balance.");
      return;
    }

    

    dispatch(
      updateProductQuantity({
        productCode,
        updatedQuantity: newQuantity,
        updatedAmount: updatedAmount,
      })
    );
  };

  const handleDecreaseQuantity = (productCode, currentQuantity, faceValue) => {
    console.log(currentQuantity);
    const parsedFaceValue = parseFloat(faceValue);
    if (isNaN(parsedFaceValue)) {
      console.error("Invalid face value:", faceValue);
      return;
    }

    if (currentQuantity <= 1) return;

    const newQuantity = currentQuantity - 1;
    const updatedAmount = parsedFaceValue * newQuantity;

    dispatch(
      updateProductQuantity({
        productCode,
        updatedQuantity: newQuantity,
        updatedAmount: updatedAmount,
      })
    );
  };

  const handleRemoveFromCart = (productCode) => {
    dispatch(removeProductFromCart(productCode));
  };

  const [formData, setFormData] = useState({
    mobile: user.mobile_no,
    email: user.email_id,
  });
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevState) => ({
      ...prevState,
      [name]: value,
    }));
  };

  // order submit

  const handleOrderSubmit = async () => {
    
    const updatedCartItems = safeCart.map((item) => {
      return {
        sku_code: item.product.product_code,
        quantity: item.quantity,
        amount: item.product.selected_denomination.face_value,
        totalAmount: item.amount,
      };
    });

    const orderDetailsObj = {
      data: updatedCartItems,
      email: formData.email,
      mobile: formData.mobile,
      token
    };

    try {
      const response = await api.post(
        "/api/vouchers/order/place",
        orderDetailsObj
      );
      if (response.status === 200) {
        dispatch(clearCart());
         dispatch(updateWalletBalance(response.data.data.balance));
        setCartModal(false);
        setOrderModal(true);
        setOrder(response.data.data);
        
        
      }
    } catch (error) {
      
      dispatch(clearCart());
     setCartModal(false);
     dispatch(showErrorToast(error.response.data.message))
      console.log(error);
    }
  };

  const closeOrderModal = ()=>{
    setOrderModal(false)
  }

  return (
    <>
      {cartModal && (
        <div
          className={`modal fade fullscreenModal show`}
          id="Cart"
          role="dialog"
          aria-labelledby="CartModal"
          aria-hidden="true"
          style={{ display: "block" }}
        >
          <div
            className="modal-dialog modal-dialog-centered modal-lg"
            role="document"
          >
            <div className="modal-content" style={{ width: "100%" }}>
              <div className="modal-body p-3">
                <button
                  className="close-modal"
                  style={{ backgroundColor: "none", color: "none" }}
                  onClick={handleCartModalToggle}
                >
                  &times;
                </button>

                <h5
                  className="modal-title text-theme"
                  style={{ textAlign: "left" }}
                >
                  My Cart
                </h5>
                <hr />
                {safeCart.length > 0 ? (
                  <div className="ShowCart" style={{ minHeight: "78vh" }}>
                    <div className="pt-3 d-none d-lg-block rounded-top">
                      <div className="row-alt">
                        <div className="col p-0">
                          <div className="row-alt ">
                            <div className="col-12 col-lg-6 col-md-5 pr-5 pl-2">
                              <p className="m-0">
                                <b>Item Name</b>
                              </p>
                            </div>
                            <div className="col-6 col-lg-2 col-md-3 p-0 pl-2 pr-1 text-center row-alt">
                              <p className="m-0 pl-2">
                                <b>Qty</b>
                              </p>
                            </div>
                            <div className="col-3 col-lg-2 col-md-2 p-0 text-right row-alt">
                              <p className="m-0 pl-4">
                                <b>Rate</b>
                              </p>
                            </div>
                            <div className="col-3 col-lg-2 col-md-2 p-0 text-right total-amount ">
                              <p className="m-0" style={{ textAlign: "left" }}>
                                <b>Amount</b>
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="item">
                      {Array.isArray(safeCart) &&
                        safeCart.map((item) => (
                          <div
                            key={item.product.product_code}
                            className="row-alt"
                            style={{
                              height: "100px",
                              alignItems: "center",
                              borderBottom: "2px solid #000a",
                            }}
                          >
                            <div className="col-9 col-lg-6 col-md-5 pr-5 pl-2 d-flex align-items-center text-left gap-2">
                              <img
                                src={item.product.brand_image_url}
                                style={{ width: "100px" }}
                              />
                              <p className="m-0">
                                {item.product.selected_denomination.item_name}
                              </p>
                            </div>
                            <div className="col-6 col-lg-2 col-md-3 p-0 pl-2 pr-1 text-center">
                              <div className="quantity-controls">
                                <button
                                  className="decrease-btn"
                                  onClick={() =>
                                    handleDecreaseQuantity(
                                      item.product.product_code,
                                      item.quantity,
                                      item.product.selected_denomination
                                        .face_value
                                    )
                                  }
                                >
                                  -
                                </button>
                                <span>{item.quantity}</span>
                                <button
                                  className="increase-btn"
                                  onClick={() =>
                                    handleIncreaseQuantity(
                                      item.product.product_code,
                                      item.quantity,
                                      item.product.selected_denomination
                                        .face_value
                                    )
                                  }
                                >
                                  +
                                </button>
                              </div>
                            </div>
                            <div className="col-3 col-lg-2 col-md-2 p-0 text-right">
                              <p
                                className="m-0 pl-4"
                                style={{ textAlign: "left" }}
                              >
                                ₹{" "}
                                {item.product.selected_denomination.face_value}
                              </p>
                            </div>
                            <div className="col-3 col-lg-2 col-md-2 p-0 text-right d-flex gap-5">
                              <p className="m-0 " style={{ textAlign: "left" }}>
                                ₹ {item.amount}
                              </p>

                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                x="0px"
                                y="0px"
                                width="25"
                                height="25"
                                viewBox="0 0 32 32"
                                onClick={() =>
                                  handleRemoveFromCart(
                                    item.product.product_code
                                  )
                                }
                              >
                                <path d="M 15 4 C 14.476563 4 13.941406 4.183594 13.5625 4.5625 C 13.183594 4.941406 13 5.476563 13 6 L 13 7 L 7 7 L 7 9 L 8 9 L 8 25 C 8 26.644531 9.355469 28 11 28 L 23 28 C 24.644531 28 26 26.644531 26 25 L 26 9 L 27 9 L 27 7 L 21 7 L 21 6 C 21 5.476563 20.816406 4.941406 20.4375 4.5625 C 20.058594 4.183594 19.523438 4 19 4 Z M 15 6 L 19 6 L 19 7 L 15 7 Z M 10 9 L 24 9 L 24 25 C 24 25.554688 23.554688 26 23 26 L 11 26 C 10.445313 26 10 25.554688 10 25 Z M 12 12 L 12 23 L 14 23 L 14 12 Z M 16 12 L 16 23 L 18 23 L 18 12 Z M 20 12 L 20 23 L 22 23 L 22 12 Z"></path>
                              </svg>
                            </div>
                          </div>
                        ))}
                    </div>

                    <div className="pt-3" style={{ position: "relative" }}>
                      <div className="row-alt">
                        <div className="col p-0">
                          <div className="row-alt">
                            <div className="col-12 p-0 text-right total-amount ">
                              <p className="text-end mb-2 ">
                                Total Amount: <b>₹ {totalAmount} </b>
                              </p>
                              <p className="text-end mb-3 text-theme">
                                Balance Amount: <b>₹ {walletBalance}</b>
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    {redemption_type === 1 && walletBalance > 0 && (
                      <div className="balanceZhide">
                        <div className="alert alert-danger" role="alert">
                          <div className="float-left mr-1">
                            <svg
                              viewBox="0 0 24 24"
                              width="44"
                              height="44"
                              stroke="currentColor"
                              strokeWidth="2"
                              fill="none"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              className="mr-2"
                              style={{ marginBottom: "-6px" }}
                            >
                              <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
                              <line x1="12" y1="9" x2="12" y2="13"></line>
                              <line x1="12" y1="17" x2="12.01" y2="17"></line>
                            </svg>
                          </div>
                          <div>
                            <span className="">
                              You still have ₹
                              <b className="avBalnce">{walletBalance}</b>{" "}
                              balance available.{" "}
                            </span>{" "}
                            <p className="ml-3 mb-0">
                              To Confirm your order you need to use the complete
                              amount !
                            </p>
                          </div>
                        </div>

                        <button
                          type="button"
                          className="btn btn-theme ripple-effect mb-2 btn-block mt-1"
                          data-dismiss="modal"
                          aria-label="Close"
                          onClick={handleCartModalToggle}
                        >
                          Continue Selection
                        </button>
                      </div>
                    )}

                    {(redemption_type === 2 || walletBalance === 0) && (
                      <div>
                        <h6 className="mb-3">Delivery Address:</h6>
                        <form>
                          <div className="form-group row mb-0">
                            <label
                              htmlFor="mobile"
                              className="col-4 col-sm-3 col-lg-2 col-form-label"
                            >
                              Mobile No.
                            </label>
                            <div className="col-8 col-sm-9 col-lg-8">
                              <input
                                type="number"
                                id="mobile"
                                name="mobile"
                                minLength="10"
                                maxLength="10"
                                className="form-control mt-2 form-control-sm"
                                value={formData.mobile}
                                onChange={handleInputChange}
                                disabled={!isMobileChangeAllowed}
                                placeholder="Enter mobile number"
                              />
                            </div>
                          </div>
                          <div className="form-group row">
                            <label
                              htmlFor="email"
                              className="col-4 col-sm-3 col-lg-2 col-form-label mt-2"
                            >
                              Email ID
                            </label>
                            <div className="col-8 col-sm-9 col-lg-8">
                              <input
                                type="email"
                                id="email"
                                name="email"
                                className="form-control mt-2 form-control-sm"
                                value={formData.email}
                                onChange={handleInputChange}
                                disabled={!isEmailChangeAllowed}
                                placeholder="Enter email ID"
                              />
                            </div>
                          </div>
                        </form>

                        <p className="text-danger mb-0 small">
                          Note: Your email ID and mobile number will be used
                          only to share the brand code with you.
                        </p>

                        <button
                          type="button"
                          className="btn btn-theme ripple-effect mb-2 float-right btn-block mt-3"
                          onClick={handleOrderSubmit}
                        >
                          Confirm Order
                        </button>
                      </div>
                    )}
                  </div>
                ) : (
                  <div
                    className="ShowEmpty text-center"
                    style={{ minHeight: "78vh" }}
                  >
                    <img
                      className="img-fluid"
                      src="./images/empty_cart.jpg"
                      alt="Empty Cart"
                      style={{ width: "400px", zIndex: 2 }}
                    />
                    <h5>Your cart is empty</h5>
                    <button
                      type="button"
                      className="btn btn-block btn-theme ripple-effect mt-5 mb-3"
                      onClick={handleCartModalToggle}
                    >
                      Close
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {orderModal && (
        <>
          <OrderDetails order={order}  closeOrderModal={closeOrderModal} vd={voucher_delivery_mode} odp={true}/>
        </>
      )}
    </>
  );
}
