@import "tailwindcss";

/* CSS Variables for theme colors */
/* :root {
  --theme: #a80100;
  --themesecondary: #004c8f;
} */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Oxygen-Sans,
    Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
  /* color: #aaa; */
}

/* Form styling for floating labels */
.form__group {
  position: relative;
  padding: 15px 0 0;
  margin-top: 10px;
  width: 100%;
}

.form__field {
  font-family: inherit;
  width: 100%;
  border: 0;
  border-bottom: 2px solid #9b9b9b;
  outline: 0;
  font-size: 1.3rem;
  color: #555;
  padding: 7px 0;
  background: transparent;
  transition: border-color 0.2s;
}

.form__field::placeholder {
  color: transparent;
}

.form__field:placeholder-shown ~ .form__label {
  font-size: 1.3rem;
  cursor: text;
  top: 20px;
}

.form__label {
  position: absolute;
  top: 0;
  display: block;
  transition: 0.2s;
  font-size: 1rem;
  color: #777;
  font-weight: 700;
}

.form__field:focus {
  padding-bottom: 6px;
  font-weight: 700;
  border-width: 3px;
  border-image: linear-gradient(to right, #555, #555);
  border-image-slice: 1;
}

.form__field:focus ~ .form__label {
  position: absolute;
  top: 0;
  display: block;
  transition: 0.2s;
  font-size: 1rem;
  color: #555;
  font-weight: 700;
}

/* Required for form validation */
.form__field:required,
.form__field:invalid {
  box-shadow: none;
}

/* Small utility class */
.small {
  font-size: 12px;
}

/* Margin top utilities */
.mt0 {
  margin-top: 0;
}

.mt4 {
  margin-top: 40px;
}

/* Additional utility classes */
.text-gray-400 {
  color: #9ca3af;
}

.text-gray-700 {
  color: #374151;
}

.text-gray-777 {
  color: #777777;
}

.text-red-500 {
  color: #ef4444;
}

.border-gray-400 {
  border-color: #9ca3af;
}

.border-gray-700 {
  border-color: #374151;
}

.bg-gray-600 {
  background-color: #4b5563;
}

.bg-red-600 {
  background-color: #dc2626;
}

.hover\:bg-red-600:hover {
  background-color: #dc2626;
}

.hover\:text-red-600:hover {
  color: #dc2626;
}

.transition-colors {
  transition-property: color, background-color, border-color;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.duration-300 {
  transition-duration: 300ms;
}

/* Custom button styles */
.btn-submit {
  background-color: #4b5563;
  color: white;
  border: none;
  border-radius: 30px;
  padding: 0 30px;
  height: 38px;
  line-height: 36px;
  font-size: 14px;
  cursor: pointer;
  display: inline-block;
  text-align: center;
  margin-top: 40px;
  margin-bottom: 120px;
  transition: all 0.5s ease-in-out;
}

.btn-submit:hover {
  background-color: #dc2626;
  box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.5);
}

@media (max-width: 768px) {
  .btn-submit {
    margin-bottom: 30px;
    padding: 0 20px;
    height: 36px;
    line-height: 34px;
    font-size: 13px;
    margin-top: 30px;
    max-width: 180px;
    min-width: 140px;
  }
}
