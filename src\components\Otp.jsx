import React, { useEffect, useState } from 'react'
import { Link, useLocation, useNavigate } from 'react-router-dom'
import './css/OTP.css'
import axios from 'axios'
import api from '../Api'


export default function Otp() {
    const location = useLocation()
    const { userId, data,style,link_token } = location.state || {};


    const [otp, setOtp] = useState('');
  const [timer, setTimer] = useState(10); 
  const [showResend, setShowResend] = useState(false); 
  const [error, setError] = useState(false); 
  const navigate = useNavigate(); 

  useEffect(() => {
    const countdown = setInterval(() => {
      setTimer((prevTimer) => {
        if (prevTimer === 1) {
          clearInterval(countdown);
          setShowResend(true); 
        }
        return prevTimer - 1;
      });
    }, 1000);

    return () => clearInterval(countdown); 
  }, []);

  const handleSubmit = async(e) => {
    e.preventDefault();


    const formData = {
        id: userId,
        otp
      };
    try {
      const response = await api.post(
        `${import.meta.env.VITE_API_URL}/api/auth/verify-otp`,
        formData
      );
 

      if (response.status === 200) {
        const token = response.data?.data?.token;
      
        if (token) {
          localStorage.setItem("td", response.data.data.token)
          localStorage.setItem("tx", response.data.data.tokenExpiry)
      
          if (response.data.data.link_token === "1") {
            navigate(`/mono_brand_voucher`);
          } else if (response.data.data.link_token === "2") {
            navigate(`/multi_brand_voucher`);
          }
        } 
        
      }
      console.log(response);
    } catch (error) {
    setError(error.response.data.message)
      console.log("Error fetching data:", error);
    }
  };

  const handleResend = () => {
    setTimer(10);
    setShowResend(false);
  };
  return (
    <>
    <div className="columnsContainer">
      <div className="mobile_image">
        <img width="100%" src={style.login_page_banner} alt="background" />
      </div>

      <div className="rightColumn">
        <div id="DivLogin" style={{ position: 'relative', textAlign: 'center' }}>
          <p style={{ fontSize: '18px', color: '#d66159' }}>OTP VERIFICATION</p>
          <p style={{ marginBottom: '30px',marginTop:"10px" }}>An OTP has been sent to {data}</p>
          <p style={{ fontWeight: 600, color: 'black', marginBottom: '30px' }}>Please enter OTP to verify</p>

          <form onSubmit={handleSubmit}>
            <div id="divOuter">
              <div id="divInner">
                <input
                  id="partitioned"
                  type="text"
                  maxLength="4"
                  value={otp}
                  onChange={(e) => setOtp(e.target.value)}
                />
              </div>
            </div>

            {timer > 0 ? (
              <p style={{ fontSize: '12px',marginTop:"10px" }} id="resendTimer">
                Resend OTP in <span id="countdowntimer">{timer}</span> Seconds
              </p>
            ) : null}

           
            

            {showResend && (
              <a href="#" id="resend" className='mt-4' onClick={handleResend}>
                <p className="text-info font-weight-bold mt-4">Resend OTP</p>
              </a>
            )}
            

              {error && (
                <p
                  className=" small"
                  style={{
                    color: "red",
                    textAlign: "center",
                    display: "block",
                    textTransform:'capitalize'
                  }}
                >
                  {error}
                </p>
              )}
            

            <button
              type="submit"
              id="Login"
              className="submitDisable btn btn-theme btn-block ripple-effect mb-4 mt-4 border-rounded"
            >
              Verify
            </button>
          </form>

          <Link to="/" style={{ marginBottom: '40px' }}>
            <p>Go Back</p>
          </Link>
        </div>
      </div>

      <div className="leftColumn" style={{ backgroundImage: `url(${style.login_page_background
      })` }}>
        <div className="overlay">
          <img width="155%" src={style.login_page_banner} alt="overlay" />
        </div>
      </div>
    </div>
    
    </>
  )
}
