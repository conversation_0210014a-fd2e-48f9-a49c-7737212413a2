import React, { useState } from 'react'
import { useDispatch, useSelector } from 'react-redux';
import { addToCart, updateProductQuantity, updateWalletBalance,showSuccessToast,showInfoToast,showErrorToast} from '../../redux/actions';

export default function ProductModal({selectedProduct,closeModal}) {
    const [selectedAmount, setSelectedAmount] = useState(null);
  const [selectedGiftId, setSelectedGiftId] = useState(null);
  const [quantity, setQuantity] = useState(1);
  const [activeTab, setActiveTab] = useState("Description");
  // const maxQuantity = mq; // predefined max quantity

  const dispatch = useDispatch();
  const walletBalance = useSelector((state) => state.cart.walletBalance);
  const cart = useSelector((state) => state.cart.cart);
  const maxQuantity = useSelector((state) => state.cart.maxQuantity); 
  
  // Total amount calculation
  const totalAmount = selectedAmount * quantity;

  const handleAmountChange = (gift_id, face_value) => {
    setSelectedAmount(face_value);
    setSelectedGiftId(gift_id);
  };

  const handleQuantityChange = (e) => {
    const value = parseInt(e.target.value, 10);

    if (maxQuantity !== null && value > maxQuantity) {
      alert(`Quantity cannot be greater than ${maxQuantity}`);
    } else {
      setQuantity(value);
    }
  };

  // Handle adding to cart
  const handleAddToCart = () => {
    const selectedDenomination = selectedProduct.denominations.find(
        (denom) => denom.gift_id === selectedGiftId 
    );

    if (!selectedDenomination) {
      dispatch(showErrorToast("Invalid Item Selected."));

        alert("Invalid denomination selected.");
        return;
    }

    const totalAmount = selectedAmount * quantity;

    if (totalAmount > walletBalance) {
      dispatch(showErrorToast("Insufficient wallet balance."));
        
        return;
    }

    const existingProduct = cart.find(
        (item) => item.product.product_code === selectedDenomination.product_code
    );

    if (existingProduct) {

      const parsedFaceValue = parseFloat(existingProduct.product.selected_denomination.face_value);
          if (isNaN(parsedFaceValue)) {
            console.error("Invalid face value:", existingProduct.product.selected_denomination.face_value);
            return;
          }
      
          const newQuantity = existingProduct.quantity + quantity;
          console.log(newQuantity)
      
          if (newQuantity > maxQuantity) {

         
            dispatch(showInfoToast(`Cannot increase quantity beyond the maximum limit of ${maxQuantity}.`));
         
            return;
          }
      
          const updatedAmount = parsedFaceValue * newQuantity;
      
          const parsedWalletBalance = parseFloat(walletBalance);
          if (isNaN(parsedWalletBalance)) {

            alert("Invalid wallet balance.");
            return;
          }
      
       
          dispatch(
            updateProductQuantity({
              productCode:existingProduct.product.product_code,
              updatedQuantity: newQuantity,
              updatedAmount: updatedAmount,
            })
          );
        
    } else {
        if (maxQuantity !== null && quantity > maxQuantity) {
          dispatch(showInfoToast(`Cannot add more than ${maxQuantity} of this product.`));
            return; 
        }

        dispatch(addToCart({
            product: {
                brand_image_url: selectedProduct.brand_image_url,
                brand_name: selectedProduct.brand_name,
                category: selectedProduct.category,
                product_type: selectedProduct.product_type,
                how_to_redeem: selectedProduct.how_to_redeem,
                tnc: selectedProduct.tnc,
                product_code: selectedDenomination.product_code, 
                item_name: selectedDenomination.item_name,
                selected_denomination: selectedDenomination,
            },
            quantity,
            amount: selectedAmount,
        }));
    }

    const updatedBalance = walletBalance - totalAmount;
    dispatch(updateWalletBalance(updatedBalance));
    dispatch(showSuccessToast(`Item added to the cart!`));

};

  const setActiveTabHandler = (tab) => {
    setActiveTab(tab);
  };
  return (
    <>
    <div
      className="modal fade fullscreenModal bd-example-modal-lg show"
      tabIndex="-1"
      role="dialog"
      aria-labelledby="exampleModalCenterTitle"
      aria-hidden="false"
      style={{ display: "block" }}
    >
      <div
        className="modal-dialog modal-dialog-centered modal-lg"
        style={{ maxWidth: "900px" }}
      >
        <div className="modal-content">
          <div className="gift-card-modal">
            <div className="d-flex justify-content-between align-items-center">
              <h2>{selectedProduct.brand_name} Gift Card</h2>
              <button className="close-modal px-3" onClick={closeModal}>
                &times;
              </button>
            </div>
            <hr style={{ border: "-1px solid #aeeee" }} />
            <div className="modal-row">
              {/* Left Partition */}
              <div className="left-partition">
                <img
                  src={selectedProduct.brand_image_url}
                  alt={selectedProduct.brand_name}
                />

                <div className="amount-selection">
                  <p className="text-secondary font-weight-normal mb-1">
                    <b>Choose An Amount</b>
                  </p>
                  {selectedProduct.denominations.map((amount) => (
                    <button
                      key={amount.gift_id}
                      className={`amount-btn ${
                        selectedGiftId === amount.gift_id ? "selected" : ""
                      }`}
                      onClick={() => handleAmountChange(amount.gift_id, amount.face_value)}
                    >
                      ₹{amount.face_value}
                    </button>
                  ))}
                </div>

                <div className="quantity-selection">
                  <p className="mb-1 text-secondary font-weight-normal ">
                    <b>Quantity</b>
                  </p>
                  <input
                    type="number"
                    value={quantity}
                    min="1"
                    onChange={handleQuantityChange}
                  />
                </div>
                <p className="mb-2 ml-0 text-secondary text-start">
                  <b>Total Amount: </b>
                  <span className="addTCAmt">{totalAmount > 0 ? totalAmount : 0}</span>
                </p>

                <button className="add-to-cart" onClick={handleAddToCart}>
                  Add to cart
                </button>
              </div>

              {/* Right Partition */}
              <div className="right-partition">
                <div className="tabs">
                  <button
                    onClick={() => setActiveTabHandler("Description")}
                    className={activeTab === "Description" ? "active" : ""}
                  >
                    Description
                  </button>
                  <button
                    onClick={() => setActiveTabHandler("T&C")}
                    className={activeTab === "T&C" ? "active" : ""}
                  >
                    T&C
                  </button>
                  <button
                    onClick={() => setActiveTabHandler("How To Redeem")}
                    className={activeTab === "How To Redeem" ? "active" : ""}
                  >
                    How To Redeem
                  </button>
                </div>

                <div className="tab-content">
                  {activeTab === "Description" && (
                    <>
                      <div
                        className="offer-content"
                        dangerouslySetInnerHTML={{
                          __html: selectedProduct.description,
                        }}
                      />
                      <div className="redeemable">
                        <span>Redeemable:</span>
                        <span>Offline</span>
                        <span>Online</span>
                      </div>
                    </>
                  )}
                  {activeTab === "T&C" && (
                   <div
                   className="offer-content"
                   style={{overflowY:"scroll",height:"420px"}}
                   dangerouslySetInnerHTML={{
                     __html: selectedProduct.tnc,
                   }}/>
                  )}
                  {activeTab === "How To Redeem" && (
                    <div
                    className="offer-content"
                    dangerouslySetInnerHTML={{
                      __html: selectedProduct.how_to_redeem,
                    }}
                  />
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    </>
  )
}
