import React, { useEffect, useRef, useState } from "react";
import { useLocation } from "react-router-dom";
import api from "../../Api";
import "./css/Monobrand.css";
const SECRET_KEY = "your_secret_key";
import { QRCodeCanvas } from "qrcode.react";
import { useNavigate } from "react-router-dom";
import CryptoJS from "crypto-js";
const decryptData = (encryptedData) => {
  const bytes = CryptoJS.AES.decrypt(encryptedData, SECRET_KEY);
  return bytes.toString(CryptoJS.enc.Utf8);
};
export default function Monobrand() {
  const location = useLocation();
  const { data } = location.state || {};
  const [activeTab, setActiveTab] = useState("app");
  const [vouchers, setVouchers] = useState([]);
  const [count, setCount] = useState("");
  const [amount, setAmount] = useState("");
  const [settings, setSettings] = useState("");
  const [selectedVoucher, setSelectedVoucher] = useState(null);
  const [remainingVouchers, setRemainingVouchers] = useState(0);
  const [activeAccordion, setActiveAccordion] = useState(null);
  const navigate = useNavigate();

 
  const handleTabChange = (tabId) => {
    setActiveTab(tabId);
  };

   useEffect(() => {
    const handleBack = () => {
      const token = localStorage.getItem("token");
      const pin = localStorage.getItem("p");

      if (token && pin) {
        const encryptedToken = decryptData(token);

        localStorage.removeItem("td");
        localStorage.removeItem("tx");

        window.location.href = `/?v=${encryptedToken}&p=${pin}`;
      } else {
        navigate("/", { replace: true });
      }
    };

    window.history.pushState(null, "", window.location.href);
    window.addEventListener("popstate", handleBack);

    return () => {
      window.removeEventListener("popstate", handleBack);
    };
  }, [navigate]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await api.get("/api/vouchers/mono-vouchers/count");
        if (response.status === 200) {
          setAmount(response.data.data.amount);
          setCount(response.data.data.voucher_count);
          setVouchers(response.data.data.vouchers);
          setSettings(response.data.data.settings);
          setRemainingVouchers(
            response.data.data.voucher_count -
              response.data.data.vouchers.length
          );
        }
        console.log(response);
      } catch (error) {
        console.log(error);
      }
    };

    fetchData();
  }, []);

  const fetchNewVoucher = async (index) => {
    const response = await api.get("/api/vouchers/mono-vouchers/get");
    console.log(response.data.data);
    const newVoucher = await response.data.data;

    setVouchers((prevVouchers) => {
      const updatedVouchers = [...prevVouchers];
      updatedVouchers[index] = newVoucher;
      return updatedVouchers;
    });
    setRemainingVouchers((prevRemaining) => prevRemaining - 1);

    // Show modal with the newly fetched voucher
    setSelectedVoucher(newVoucher);
    setShowModal(true);
  };

  // for multiple voucher modal

  const [showModal, setShowModal] = useState(false);

  const handleShowModal = (voucher) => {
    setSelectedVoucher(voucher);
    setShowModal(true);
  };

  const handleCloseModal = () => {
    setShowModal(false);
  };

  // copy
  const [showToast, setShowToast] = useState(false);
  const copyToClipboard = async (text) => {
    try {
      await navigator.clipboard.writeText(text);
      setShowToast(true);
      setTimeout(() => setShowToast(false), 1000); // Reset after 2 seconds
    } catch (err) {
      console.error("Failed to copy: ", err);
    }
  };
  const updatedTemplate = settings.template
    ? settings.template
        .replace(/{product_name}/g, settings.content.brand_name)
        .replace(/{how_to_redeem}/g, settings.content.how_to_redeem) 
      .replace(/{terms_and_condition}/g, settings.content.terms_conditions)
    : "";

  const initAccordion = () => {
    const accordionTriggers = document.querySelectorAll(".accordion__trigger");
    accordionTriggers.forEach((trigger) => {
      trigger.addEventListener("click", () => {
        const expanded = trigger.getAttribute("aria-expanded") === "true";
        trigger.setAttribute("aria-expanded", !expanded);
        const copy = trigger
          .closest(".accordion__item")
          .querySelector(".accordion__copy");
        copy.style.display = expanded ? "none" : "block";
      });
    });
  };

  // Initialize accordion after content is rendered
  useEffect(() => {
    if (settings) {
      initAccordion();
    }
  }, [settings]);

  return (
    <>
      <div className="columnsContainer_mono row">
        <div className="mobile_image"></div>

        <div className="leftColumn_mono">
          <div
            style={{ paddingLeft: "5px" }}
            dangerouslySetInnerHTML={{ __html: updatedTemplate }}
          ></div>
        </div>

        <div className="rightColumn_mono">
          {/* for single voucher */}
          {count === 1 && (
            <>
              <div className="rightColumn-envelop">
                <img
                  className="brand"
                  alt="image"
                  src={settings.content.image_name}
                />
                <div className="coupon_p1">
                  <h3 className="color-black mb1">Rs. {amount} Gift Card</h3>
                  <div style={{ margin: "20px 0" }}>
                    <QRCodeCanvas value={vouchers[0].voucher_code} size={128} />
                  </div>
                  <p className="small" style={{ marginBottom: "10px" }}>
                    Valid Till:{" "}
                    {new Date(
                      new Date(vouchers[0].expiry_date)
                        .toISOString()
                        .split("T")[0]
                    ).toLocaleDateString("en-IN", {
                      month: "short",
                      day: "numeric",
                      year: "numeric",
                      timeZone: "Asia/Kolkata",
                    })}
                  </p>
                </div>
                <div class="coupon_p2_s">
                  <div class="code_label">Code </div>
                  <div class="code">{vouchers[0].voucher_code}</div>

                  <div className="copy_s">
                    <a
                      href="#"
                      onClick={() => copyToClipboard(vouchers[0].voucher_code)}
                    >
                      <svg
                        viewBox="0 0 24 24"
                        width="16"
                        height="16"
                        stroke="currentColor"
                        strokeWidth="1"
                        fill="none"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="color-black"
                      >
                        <rect
                          x="9"
                          y="9"
                          width="13"
                          height="13"
                          rx="2"
                          ry="2"
                        ></rect>
                        <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                      </svg>
                    </a>
                  </div>
                </div>
                {/* <div class="row"> */}
                <div className="coupon_p3_s"></div>
                <div className="coupon_p4_s small ls1">
                  <b style={{ paddingLeft: "30px", color: "black" }}>PIN</b> :
                  {vouchers[0].pin}
                  <a href="#" class="copy_pin">
                    <svg
                      viewBox="0 0 24 24"
                      width="14"
                      height="14"
                      stroke="currentColor"
                      stroke-width="1"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      className="color-black"
                      onClick={(e) => copyToClipboard(vouchers[0].pin)}
                    >
                      <rect
                        x="9"
                        y="9"
                        width="13"
                        height="13"
                        rx="2"
                        ry="2"
                      ></rect>
                      <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                    </svg>
                  </a>
                  <hr style={{ border: "-1px dashed #eeeeee" }} />
                  {/* <br /> */}
                  <p className="small mt0 ls1">
                    <b>Ref-ID</b> : {vouchers[0].unique_reference_id}{" "}
                  </p>
                </div>
                <div className="coupon_p5_s"></div>
                {showToast && (
                  <div className="toast show">Copied to clipboard!</div>
                )}
              </div>
            </>
          )}

          {/* for  multi voucher */}

          {count > 1 && (
            <>
              <div className="rightColumn-envelop">
                <img
                  width="75%"
                  className="brand"
                  src={settings.content.image_name}
                />
                <div className="coupon_p1">
                  <h3 className="color-black mb0">Rs.{amount} E-Gift Card</h3>
                  <h4 className="color-theme mb3 mt1">Quantity : {count}</h4>

                  {Array.from({ length: count }).map((_, index) => {
                    const voucher = vouchers[index];
                    return (
                      <div
                        className="voucherContainer small"
                        onClick={
                          voucher
                            ? () => handleShowModal(voucher)
                            : () => fetchNewVoucher(index)
                        }
                        key={index}
                      >
                        <div className="voucherContainerdiv1">
                          # {index + 1}
                        </div>
                        <div className="voucherContainerdiv2">View Code</div>
                        <div className="voucherContainerdiv3">
                          <img
                            src={`./images/${
                              voucher ? "opened" : "closed"
                            }.png`}
                            alt={`Voucher ${index + 1}`}
                          />
                        </div>
                      </div>
                    );
                  })}
                </div>
                <div className="coupon_p2"></div>
                <div className="">
                  <div className="coupon_p3"></div>
                  <div className="coupon_p4 small ls1">
                    <hr style={{ border: "1px dashedrgb(165, 165, 165)" }} />
                    {/* <br /> */}
                    <a href="#" className="small mb0 color-black ">
                      <b>View Terms & Conditions</b>
                    </a>
                  </div>
                  <div className="coupon_p5"></div>
                </div>
              </div>
            </>
          )}
        </div>

        {/* Modal Component */}
        {showModal && selectedVoucher && (
          <div className="modal">
            <div
              className="modal-bg modal-exit"
              onClick={handleCloseModal}
            ></div>
            <div className="modal-container">
              <div className="coupon_p1_modal">
                <div className="close_modal" onClick={handleCloseModal}>
                  <svg
                    viewBox="0 0 24 24"
                    width="24"
                    height="24"
                    stroke="currentColor"
                    strokeWidth="1"
                    fill="none"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="modal-exit"
                  >
                    <circle cx="12" cy="12" r="10"></circle>
                    <line x1="15" y1="9" x2="9" y2="15"></line>
                    <line x1="9" y1="9" x2="15" y2="15"></line>
                  </svg>
                </div>
                <h3 className="voucherNo mb1 mt0">
                  Gift Card #{selectedVoucher.id}
                </h3>
                <h3 className="color-black mb1">Rs.{amount} Gift Card</h3>
                <QRCodeCanvas
                  value={selectedVoucher.voucher_code} // Use voucher code as the value
                  size={128} // Size of the QR code
                  level="H" // Error correction level (optional)
                  renderAs="svg" // Render as SVG
                />
                <p className="small" style={{ marginBottom: "10px" }}>
                  Valid till :{" "}
                  {new Date(
                    new Date(selectedVoucher.expiry_date)
                      .toISOString()
                      .split("T")[0]
                  ).toLocaleDateString("en-IN", {
                    month: "short",
                    day: "numeric",
                    year: "numeric",
                    timeZone: "Asia/Kolkata",
                  })}
                </p>
                <p className="small mt0 ls1">
                  GCID : {selectedVoucher.unique_reference_id}
                </p>
              </div>
              <div className="coupon_p2_modal">
                <div className="code_label">Code </div>
                <div className="code">{selectedVoucher.voucher_code}</div>
                <div className="copy">
                  <a
                    href="#"
                    onClick={() =>
                      copyToClipboard(selectedVoucher.voucher_code)
                    }
                  >
                    <svg
                      viewBox="0 0 24 24"
                      width="16"
                      height="16"
                      stroke="currentColor"
                      strokeWidth="1"
                      fill="none"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="color-black"
                    >
                      <rect
                        x="9"
                        y="9"
                        width="13"
                        height="13"
                        rx="2"
                        ry="2"
                      ></rect>
                      <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                    </svg>
                  </a>
                </div>
              </div>
              <div className="coupon_p3_modal"></div>
              <div className="coupon_p4_modal small ls1">
                <b style={{ paddingLeft: "30px" }}>PIN</b> :{" "}
                {selectedVoucher.pin}
                <a
                  href="#"
                  className="copy_pin"
                  onClick={() => copyToClipboard(selectedVoucher.pin)}
                >
                  <svg
                    viewBox="0 0 24 24"
                    width="14"
                    height="14"
                    stroke="currentColor"
                    strokeWidth="1"
                    fill="none"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="color-black"
                  >
                    <rect
                      x="9"
                      y="9"
                      width="13"
                      height="13"
                      rx="2"
                      ry="2"
                    ></rect>
                    <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                  </svg>
                </a>
                <hr
                  style={{
                    border: "1px dashed rgb(202, 202, 202)",
                    margin: "2px 0px",
                  }}
                />
                <br />
                <a
                  href="#"
                  className="mb0 color-theme modal-exit"
                  onClick={handleCloseModal}
                >
                  <b>CLOSE</b>
                </a>
              </div>
              <div className="coupon_p5_modal"></div>
            </div>
            {showToast && (
              <div className="toast show">Copied to clipboard!</div>
            )}
          </div>
        )}
      </div>
    </>
  );
}
