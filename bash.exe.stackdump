Stack trace:
Frame         Function      Args
0007FFFFB6A0  00021006116E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFFA5A0) msys-2.0.dll+0x2116E
0007FFFFB6A0  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFFB6A0  0002100469F2 (00021028DF99, 0007FFFFB558, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFFB6A0  00021006A3FE (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A3FE
0007FFFFB6A0  00021006A525 (0007FFFFB6B0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A525
0001004F94B7  00021006B985 (0007FFFFB6B0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B985
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF96BFB0000 ntdll.dll
7FF96A050000 KERNEL32.DLL
7FF9690A0000 KERNELBASE.dll
7FF969C40000 USER32.dll
7FF969890000 win32u.dll
7FF96ABF0000 GDI32.dll
7FF969760000 gdi32full.dll
7FF969A30000 msvcp_win.dll
7FF969480000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF96B500000 advapi32.dll
7FF96B130000 msvcrt.dll
7FF96B2E0000 sechost.dll
7FF9698C0000 bcrypt.dll
7FF96B6A0000 RPCRT4.dll
7FF968770000 CRYPTBASE.DLL
7FF9695A0000 bcryptPrimitives.dll
7FF969E00000 IMM32.DLL
