import React, { useEffect, useState } from 'react'
import axios from "axios";
import { useNavigate } from "react-router-dom";
export default function OrderDetails({order,closeOrderModal,vd,odp,resData,data}) {
  const[isConfirmOrderVisible,setIsConfirmOrderVisible] = useState(false)
  useEffect(() => {
    setIsConfirmOrderVisible(odp);
  
    if (odp) {
      const timer = setTimeout(() => {
        setIsConfirmOrderVisible(false); 
      }, 2000); 
  
      return () => clearTimeout(timer);
    }
  }, [odp]);
  const showError = (errorMessage) => {
    alert(`Error: ${errorMessage}`);
  };

  // Function to handle success button click
    const navigate = useNavigate();
  const showVouchers = async (tokenObj) => {
  const voucherCode = tokenObj.token;
 const contactInfo = String(tokenObj.pin);


  const formData = {
    voucher_code: voucherCode,
    pin: contactInfo,
  };

  try {
    const response = await axios.post(
      `${import.meta.env.VITE_API_URL}/api/auth/login`,
      formData
    );

    if (response.status === 200) {
      const tokenData = response.data?.data?.tokenData;
      console.log(tokenData);

      if (tokenData) {
        localStorage.setItem("tx", tokenData.tokenExpiry);
        localStorage.setItem("td", tokenData.token);

        if (response.data.data.link_token === "1") {
          navigate(`/mono_brand_voucher`);
        } 
      
      }
    }
  } catch (error) {
    alert(error.response?.data?.message || "Something went wrong");
    console.log("Error fetching data:", error);
  }
};


  return (
   <>
   

<div
            className={`modal fade fullscreenModal ${
              order || resData ? "show" : ""
            }`}
            tabIndex="-1"
            role="dialog"
            aria-labelledby="exampleModalCenterTitle"
            aria-hidden={order || resData ? "false" : "true"}
            style={{ display: order || resData ? "block" : "none"}}
          >
            <div
              className="modal-dialog modal-dialog-centered modal-lg"
              role="document"
            >
              <div className="modal-content">
                <div className="modal-body p-3">
                  {isConfirmOrderVisible && (
                    <div
                      id="confirm_order"
                      className="row text-center p-3"
                      style={{ display: "block" }}
                    >
                      <div className="col-12">
                        <div>
                          <svg
                            className="checkmark"
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 52 52"
                          >
                            <circle
                              className="checkmark__circle"
                              cx="26"
                              cy="26"
                              r="25"
                              fill="none"
                            />
                            <path
                              className="checkmark__check"
                              fill="none"
                              d="M14.1 27.2l7.1 7.2 16.7-16.8"
                            />
                          </svg>
                        </div>
                        <h2 className="my-2 font-weight-normal">Success!</h2>
                        <p className="p-2">
                          We have received your order and have sent a
                          confirmation to your e-mail. Your order is under
                          process and will be delivered to you soon.
                        </p>
                      </div>
                    </div>
                  )}

                  <div
                    id="order_history"
                    className=""
                    style={{ minHeight: "77vh" }}
                  >
                    <div className="container">
                      <div>
                        <div className="d-flex justify-content-between">
                          <h5 className="text-theme mt-1">Order Details</h5>

                          <button
                          className="close-modal"
                          style={{ backgroundColor: "none", color: "none" }}
                            onClick={closeOrderModal}
                          >
                            <span aria-hidden="true">&times;</span>
                          </button>
                        </div>
                        <hr />
                        <div className="pt-4 d-none d-md-block rounded-top">
                          <div className="row-alt">
                            
                            <div className="col-12 col-lg-6 col-md-6 pl-0 text-left">
                              <p>
                                <b>Item Name</b>
                              </p>
                            </div>
                            <div className="col-6 col-lg-1 col-md-1 text-right row-alt pl-0">
                              <p className="text-right">
                                <b>Qty</b>
                              </p>
                            </div>
                            <div className="col-3 col-lg-1 col-md-1 text-right pl-0">
                              <p>
                                <b>Rate</b>
                              </p>
                            </div>
                            <div className="col-3 col-lg-2 col-md-2 text-right">
                              <p>
                                <b>Amount</b>
                              </p>
                            </div>
                            {vd === 1 ||
                            vd === 3 ? (
                              <div className="col-3 col-lg-2 col-md-2 text-right pr-0">
                                <p>
                                  <b>Status</b>
                                </p>
                              </div>
                            ) : <>
                            {
                              resData && (
                                <div className="col-3 col-lg-2 col-md-2 text-right pr-0">
                                  <p>
                                    <b>Status</b>
                                  </p>
                                </div>
                              )
                            }
                            </>}
                          </div>
                        </div>
                        <hr className="m-0 d-none d-md-block" />
                        <div id="finalOrder" style={{overflowY:"scroll",height:"60vh"}}>
                        
                          {order &&
                            order.response.errors.map((item, index) => (
                              <div
                                className="row-alt pt-3 border-bottom pb-1 align-items-center"
                                key={index}
                              >
                                <div className="col-12 col-lg-6 col-md-6 pl-0">
                                  <p>
                                    {item.product_name}
                                    
                                  </p>
                                </div>
                                <div className="col-3 col-lg-1 col-md-1 pr-0 text-center row-alt pl-0">
                                  <p>{item.quantity} </p>
                                </div>
                                <div className="col-3 col-lg-1 col-md-1 text-right pl-0">
                                  <p>{item.amount}</p>
                                </div>
                                <div className="col-3 col-lg-2 col-md-2 text-right">
                                  <p>
                                  
                                    <b>{item.totalAmount}</b>
                                  </p>
                                </div>
                                <div className="col-3 col-lg-2 col-md-2 text-right  py-2">

{/*                                   
                                  {vd === 1 ||
                                  vd === 3 ? (
                                    
                                      <button
                                        type="button"
                                        className="btn btn-sm btn-theme"
                                        onClick={() => showError(item.error_message)}
                                      >
                                        View
                                      </button>
                                    
                                  
                                  ) : null} */}
                                 { item.error}
                                </div>
                              </div>
                            ))}
                          {order &&
                            order.response.success.map((item, index) => (
                              <div
                                className="row-alt pt-3 border-bottom pb-1 align-items-center"
                                key={index}
                              >
                                <div className="col-12 col-lg-6 col-md-6 pl-0">
                                  <p>
                                    {item.product_name}
                                    
                                  </p>
                                </div>
                                <div className="col-3 col-lg-1 col-md-1 pr-0 text-center row-alt pl-0">
                                  <p>{item.quantity} </p>
                                </div>
                                <div className="col-3 col-lg-1 col-md-1 text-right pl-0">
                                  <p>{item.amount}</p>
                                </div>
                                <div className="col-3 col-lg-2 col-md-2 text-right">
                                  <p>
                                  
                                    <b>{item.totalAmount}</b>
                                  </p>
                                </div>
                                <div className="col-3 col-lg-2 col-md-2 text-right  py-2">
                                {vd === 1 ||
                                  vd === 3 ? (
                                    
                                      <button
                                        type="button"
                                        className="btn btn-sm btn-theme"
                                        onClick={() => showVouchers(item.token)}
                                      >
                                        View
                                      </button>
                                    
                                  
                                  ) : null}
                                    
                                  
                                 
                                </div>
                              </div>
                            ))}
                          {resData &&
                            resData.map((item, index) => (
                              <div
                                className="row-alt pt-3 border-bottom pb-1 align-items-center"
                                key={index}
                              >
                                <div className="col-12 col-lg-6 col-md-6 pl-0">
                                  <p>
                                    {item.name}
                                    
                                  </p>
                                </div>
                                <div className="col-3 col-lg-1 col-md-1 pr-0 text-center row-alt pl-0">
                                  <p>{item.quantity} </p>
                                </div>
                                <div className="col-3 col-lg-1 col-md-1 text-right pl-0">
                                  <p>{item.rate}</p>
                                </div>
                                <div className="col-3 col-lg-2 col-md-2 text-right">
                                  <p>
                                  
                                    <b>{item.amount}</b>
                                  </p>
                                </div>
                                <div className="col-3 col-lg-2 col-md-2 text-right  py-2">
                                  {/* {console.log(item.vouchers)} */}
                                 
                                      <button
                                        type="button"
                                        className="btn btn-sm btn-theme"
                                        onClick={() => showVouchers(item.linkToken)}
                                      >
                                        View
                                      </button>
                                    
                                  
                                 
                                </div>
                              </div>
                            ))}
                        </div>
                        
                        <h6 className="text-theme my-3">Delivery Address:</h6>
                        <p className="mb-2">
                          <b>Email ID: </b>
                          <span id="confirmEmail">
                            {order && order.email
                              }
                            {data && data.email_id
                              }
                          </span>
                        </p>
                        <p>
                          <b>Mobile No: </b>
                          <span id="confirmMobile">
                          {order && order.mobile
                              }
                          {data && data.mobile_no
                              }
                          </span>
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="modal-footer py-2">
                  <button
                    type="button"
                    className="btn btn btn-secondary ripple-effect"
                    onClick={closeOrderModal}
                  >
                    <span>Close</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
   </>
  )
}
