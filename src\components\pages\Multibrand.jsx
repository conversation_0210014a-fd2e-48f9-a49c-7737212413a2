import React, { useState, useEffect } from "react";
import { BsThreeDotsVertical } from "react-icons/bs";
import "./css/Multibrand.css";
import "./css/Theme.css";
import api from "../../Api";
import ProductData from "./ProductData";
import { useDispatch, useSelector } from "react-redux";
import { updateWalletBalance, updateMaxQuantity } from "../../redux/actions";
import Cart from "./Cart";
import CryptoJS from 'crypto-js';
import History from "./History";
const SECRET_KEY = 'your_secret_key';
const encryptData = (data) => {
  return CryptoJS.AES.encrypt(data, SECRET_KEY).toString();
};

const decryptData = (encryptedData) => {
  const bytes = CryptoJS.AES.decrypt(encryptedData, SECRET_KEY);
  return bytes.toString(CryptoJS.enc.Utf8); 
};
// toast

import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';




export default function Multibrand() {
  const [showToast, setShowToast] = useState(true);
  const [data, setData] = useState("");
  const [product, setProduct] = useState("");

  const dispatch = useDispatch();
  const walletBalance = useSelector((state) => state.cart.walletBalance);
  const cartLength = useSelector((state) => state.cart.cartLength);

  //   get dashboard details

  useEffect(() => {
    const encryptedToken = localStorage.getItem('token');
let decryptedToken;
    if (encryptedToken) {
      try {
        decryptedToken = decryptData(encryptedToken);
       
      } catch (error) {
        console.error('Decryption failed:', error);
      }
    }
    
    
    const fetchData = async () => {
      try {
        const response = await api.post("/api/multibrand/dashboard/details",{decryptedToken});
        if (response.status === 200) {
         
          setData(response.data.data.output);
          setProduct(response.data.data.products);
          // dispatch(updateWalletBalance(10000));
            dispatch(updateWalletBalance(response.data.data.output.user.wallet_amount));
          dispatch(
            updateMaxQuantity(response.data.data.output.max_product_qty)
          );
          dispatch(
            updateMaxQuantity(100)
          );
        }
      } catch (error) {
        console.log(error);
      }
    };
    

    fetchData();
  }, []);

  useEffect(() => {
    if (cartLength > 0) {
      setShowToast(true); 
      setTimeout(() => setShowToast(false), 2000); 
    }
  }, [cartLength]);

  const [threeDots, setThreeDots] = useState(false);
  const [showCartModal, setShowCartModal] = useState(false);
  const handleCartModalToggle = () => {
    setShowCartModal(!showCartModal);
  };

  // for history
  const [showHistoryModal, setShowHistoryModal] = useState(false);
  const handleHistoryModalToggle = () => {
    setShowHistoryModal(!showHistoryModal);
  };
  return (
    <>
      {/* Top  banner */}

      <div style={{ backgroundColor: "#ffffff !important" }}>
        <div className="container">
          <div className="row py-2 ">
            <div className="col-5 pr-0">
              <a>
                <img
                  className="card-img-top img-fluid logo c-p"
                  src={data.logo}
                  alt="Logo"
                />
              </a>
            </div>
            <div
              className="col-7 pt-4 pr-3 text-right pl-0 gap-4"
              style={{ display: "flex", justifyContent: "end" }}
            >
              {showToast && (
                <div
                  className="toast"
                  role="alert"
                  aria-live="assertive"
                  aria-atomic="true"
                  data-delay="2000"
                  data-animation="true"
                 
                >
                  <div className="toast-body px-2 pt-3 pb-3 bg-success">
                    <div className="px-4">
                      <svg
                        className="checkmark float-left mr-2"
                        style={{
                          width: "30px",
                          height: "30px",
                          marginTop: "-10px",
                        }}
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 52 52"
                      >
                        <circle
                          className="checkmark__circle"
                          cx="26"
                          cy="26"
                          r="25"
                          fill="none"
                        />
                        <path
                          className="checkmark__check"
                          fill="none"
                          d="M14.1 27.2l7.1 7.2 16.7-16.8"
                        />
                      </svg>
                      <span className="text-white mb-4">
                        <b>Item added to your cart !</b>
                      </span>
                    </div>
                  </div>
                </div>
              )}
              <a
                id="order_history_link"
                className="ripple-effect c-p pl-3 mr-4"
                data-toggle="modal"
                data-target="#HistoryModal"
                style={{ textDecoration: "none" }}
                onClick={() => {
                  setShowHistoryModal(!showHistoryModal);
                }}
              >
                <span className=" align-top c-p font-weight-normal text-dark">
                  Order History
                </span>
              </a>
              <a
                id="cartIcon"
                className="ripple-effect c-p pl-3 mr-4"
                data-toggle="modal"
                data-target="#Cart"
                onClick={() => {
                  setShowCartModal(!showCartModal);
                }}
                style={{ textDecoration: "none" }}
              >
                <span className=" align-top c-p font-weight-normal text-dark">
                  My Cart
                </span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="18"
                  height="18"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="feather feather-shopping-cart"
                >
                  <circle cx="9" cy="21" r="1"></circle>
                  <circle cx="20" cy="21" r="1"></circle>
                  <path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
                </svg>
                <span
                  id="cartQty"
                  className="navbar-icon-badge bg-theme text-white font-weight-bold"
                  style={{ opacity: "1" }}
                >
                  {cartLength}
                </span>
              </a>
              <div className="float-right" style={{ cursor: "pointer" }}>
                <BsThreeDotsVertical
                  style={{ fontSize: "18px" }}
                  onClick={() => {
                    setThreeDots(!threeDots);
                  }}
                />
                {threeDots && (
                  <>
                    <div className={`dropdown-menu ${threeDots ? "show" : ""}`}>
                      {/* Help */}
                      <a
                        className="ripple-effect c-p dropdown-item py-2 "
                        href="#"
                        style={{ textDecoration: "none" }}
                      >
                        <svg
                          viewBox="0 0 24 24"
                          width="16"
                          height="16"
                          stroke="currentColor"
                          strokeWidth="2"
                          fill="none"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="css-i6dzq1 "
                        >
                          <circle cx="12" cy="12" r="10"></circle>
                          <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                          <line x1="12" y1="17" x2="12.01" y2="17"></line>
                        </svg>
                        <span
                          className="ml-2 small"
                          style={{ marginLeft: "7px" }}
                        >
                          Help
                        </span>
                      </a>

                      {/* Logout */}
                      <a
                        className="ripple-effect c-p dropdown-item py-2"
                        href="#"
                        style={{ textDecoration: "none" }}
                      >
                        <svg
                          viewBox="0 0 24 24"
                          width="16"
                          height="16"
                          stroke="currentColor"
                          strokeWidth="2"
                          fill="none"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="css-i6dzq1"
                        >
                          <path d="M18.36 6.64a9 9 0 1 1-12.73 0"></path>
                          <line x1="12" y1="2" x2="12" y2="12"></line>
                        </svg>
                        <span
                          className="ml-2 small"
                          style={{ marginLeft: "7px" }}
                        >
                          Logout
                        </span>
                      </a>

                      <span className="dropdown-item small py-3 text-theme">
                        <span>
                          <b>Last Logged In </b>
                          <br />
                          27/07/2020 5:33PM
                        </span>
                      </span>
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* mid banner */}

      <div id="topbanner">
        <div
          id="topbanner_desktop"
          className="row-alt"
          style={{
            paddingTop: "40px",
            height: "200px",
            backgroundColor: "#a80100",
            backgroundPosition: "center",
            backgroundRepeat: "no-repeat",
            backgroundSize: "1267px 200px",
            backgroundImage: `url(${data.banner_image})`,
          }}
        >
          <div className="col-12 pr-4 container text-end">
            <span className="badge badge-pill bg-white">
              <h4 className="mb-0 px-4 py-1" style={{ fontWeight: "600" }}>
                <img
                  className="img-fluid logo"
                  src="https://d1jj76g3lut4fe.cloudfront.net/processed/thumb/P4LuC7PSq8e8X5w5N8.png"
                  alt="Please wait"
                  style={{ width: "25px", marginTop: "-10px" }}
                />
                <span id="balance_desktop" style={{ color: "black" }}>
                  {/* {data && data.user.wallet_amount} */}
                  {walletBalance}
                </span>
              </h4>
            </span>
            <p className=" mb-1 mt-2 mr-2" style={{ color: "#ffffff" }}>
              <b>Expires On :</b> 31 Dec 2022
            </p>
            <h4
              className="mt-3"
              style={{
                color: "#ffffff",
                fontWeight: "600",
                lineHeight: "32px",
              }}
            >
              {data.banner_message}
            </h4>
          </div>
        </div>
        <div
          id="topbanner_mobile"
          className="row-alt"
          style={{
            paddingTop: "40px",
            height: "200px",
            backgroundColor: "#a80100",
            backgroundPosition: "center",
            backgroundRepeat: "no-repeat",
            backgroundSize: "769px 200px",
            backgroundImage: `url(${data.banner_image})`,
          }}
        >
          <div className="col-12 pr-3 container text-right">
            <span className="badge badge-pill bg-white">
              <h4 className="mb-0 px-4 py-1" style={{ fontWeight: "600" }}>
                <img
                  className="img-fluid logo"
                  src="https://d1jj76g3lut4fe.cloudfront.net/processed/thumb/P4LuC7PSq8e8X5w5N8.png"
                  alt="Please wait"
                  style={{ width: "25px", marginTop: "-10px" }}
                />
                {data && data.user.wallet_amount}
              </h4>
            </span>
            <p className=" mb-1 mt-2 mr-2" style={{ color: "#ffffff" }}>
              <b>Expires On :</b> 31 Dec 2022
            </p>
            <h4
              className="mt-3"
              style={{
                color: "#ffffff",
                fontWeight: "600",
                lineHeight: "32px",
              }}
            >
              {data.banner_message}
            </h4>
          </div>
        </div>
      </div>

      {/* product catalogue */}

      <ProductData productData={product}  />

      {/* cart section ------- */}

      {showCartModal && <Cart handleCartModalToggle={handleCartModalToggle} rt={data && data} />}

      {/* hisotry section ------- */}
      {showHistoryModal && <History handleHistoryModalToggle={handleHistoryModalToggle} rt={data && data}/>}

      {/* footer  */}

      <div
        className="navbar navbar-dark mt-4 px-0 "
        style={{ backgroundColor: "#999999", bottom: "-20px" }}
      >
        <div className="container text-center">
          <span className="text-white mx-auto py-1">
            © All Rights Reserved{" "}
            <a href="#" className="text-white">
              REWARDGENIX Pvt. Ltd.
            </a>
          </span>
        </div>
      </div>
      
      <ToastContainer position="top-right" autoClose={2000} style={{zIndex:"1100"}} />
    </>
  );
}
